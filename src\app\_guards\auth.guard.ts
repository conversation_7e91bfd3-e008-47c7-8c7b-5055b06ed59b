import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
// import { PermissionsService } from '../services/permissions.service';
import { AuthService } from '@auth0/auth0-angular';
import { Auth0Service } from '../services/auth0.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { UserService } from '../services/user.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AppAuthGuard {
  constructor(
    private router: Router,
    private snackBar: MatSnackBar,
    public auth: AuthService,
    public auth0: Auth0Service,
    // public permissionService: PermissionsService,
    public userService: UserService
  ) {}
  subscriptionId;
  user;
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    this.user = JSON.parse(localStorage.getItem('user'));
    this.subscriptionId = route.queryParams.sub;
    // proceed only if subs id present
    if (this.subscriptionId) {
      console.log(1);      
      localStorage.setItem('SubscriptionID', this.subscriptionId);
      return new Promise((resolve, reject) => {
        // check if user permissions are already available
        if (!this.userService.appPermissions) {
          // get app permissions
          this.userService
            .getAppPermissions(this.subscriptionId)
            .toPromise()
            .then((resp) => {
              // get url tree and append subs id
              resolve(true);
              const urlTree = this.router.parseUrl(state.url);
              urlTree.queryParams['sub'] = this.subscriptionId;
              return urlTree;
            })
            .catch((err) => {
              this.rejectAuthentication($localize`User logged out`);
              resolve(err);
            });
        } else {
          resolve(true);
        }
      });
    } else {
      this.snackBar.open(
        $localize`Missing Subscription, Redirecting ...`,
        'OK',
        {
          duration: 3000,
          panelClass: ['error-snackbar'],
        }
      );
      this.redirectUser();
    }
  }

  /***
   * Reject User Auth
   */
  rejectAuthentication = (errResp) => {
    this.auth0.logUserOut();
    this.snackBar.open(errResp, 'OK', {
      duration: 3000,
      panelClass: ['error-snackbar'],
    });
  };

  /**
   * Redirect user when no module subscription found
   */
  redirectUser = () => {
    this.user = JSON.parse(localStorage.getItem('user'));
    if (this.user.return_to) {
      console.log(this.user.return_to);
      window.location.href = 'https://' + this.user.return_to;
    } else {
      window.location.href = 'https://' + environment.default_return_url;
    }
  };
}
