import { Injectable } from '@angular/core';
import { catchError, concatMap, map, mergeMap } from 'rxjs/operators';
import {
  HttpErrorResponse,
  HttpHeaders,
  HttpClient,
} from '@angular/common/http';
import { concat, of, throwError, Observable } from 'rxjs';
import { Globals } from '../_globals/endpoints.global';

@Injectable({
  providedIn: 'root',
})
export class FileUploadService {
  private httpOptions: HttpHeaders;
  private chunkSize: number = 1 * 1024 * 1024;
  private totalChunk;
  startSize;
  endSize;
  private totalSent = 0;
  file: File;
  signedURL;
  sessionUri;
  subscription_id: any;
  batch_id: any;

  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/octet-stream',
      'x-goog-resumable': 'start',
    });
  }

  /**
   *
   * @param file
   * @param moduleId
   * @returns signed url => session uri to upload file
   */
  getSignedUrl = (
    file,
    subscription_id,
    name,
    scBatchId,
    inputTempId,
    outputTempId,
    description,
    reference
  ): Observable<any> => {
    const options = {
      headers: this.httpOptions,
      observe: 'response' as 'body',
    };
    const getSignedUrlEndpoint = this.globals.urlJoin(
      'file_upload',
      'get_signed_url'
    );
    return this.http
      .post(getSignedUrlEndpoint + subscription_id + '/upload', {
        filename: file.name,
        size: file.size,
        batch_name: name,
        sc_batch_id: scBatchId,
        input_template_id: inputTempId,
        output_template_id: outputTempId,
        description: description,
        reference_batch_id: reference,
      })
      .pipe(
        mergeMap((obj) => {
          this.batch_id = obj['batch_id'];
          return this.http.post(obj['signed_url'], null, options);
        }),
        catchError((err) => throwError(err))
      );
  };
  signed_url;

  getSessionURI = (url, file) => {
    const options = {
      headers: this.httpOptions,
      observe: 'response' as 'body',
    };
    return this.http.post(url, null, options).pipe(
      mergeMap((response: any) => {
        // console.log('got session uri', response.headers);
        // console.log('got session uri', response.headers.get('location'));
        this.sessionUri = response.headers.get('location');
        // console.log(this.sessionUri);
        this.file = file;
        this.startSize = 0;
        this.endSize = 0;
        this.totalSent = 0;
        // this.subscription_id = subscription_id;
        return this.createAndUploadChunk(file, this.sessionUri);
      }),
      concatMap((r) => r),
      map(this.updateProgress),
      catchError((err) => throwError(err))
    );
  };

  /**
   *
   * @param status
   * @returns
   */
  private updateServer = (status): Observable<void> => {
    const endpoint = this.globals.urlJoin('file_upload', 'update_server');
    return this.http.patch<void>(endpoint, status);
  };

  /**
   *
   * @param response
   * @returns the file upload progress
   */
  private updateProgress = (response: any): number => {
    if (response) {
      // this.updateServer({ status: 'succeeded' }).subscribe();
    }
    this.totalSent += 1;
    const uploadPercentage = (this.totalSent / this.totalChunk) * 100;
    return uploadPercentage;
  };

  /**
   *
   *upload file complete
   */
  uploadCompelte = (fromComments?:boolean, commentID?, subscriptionID?) => {
    const uploadCompleteEndpoint = this.globals.urlJoin(
      'file_upload',
      'upload_complete'
    );
    let endpoint;
    if(fromComments) {
      endpoint = uploadCompleteEndpoint +
      subscriptionID +
      '/comments/' +
      commentID + '/attachments' +
      '/upload_complete'
    } else {
      endpoint = uploadCompleteEndpoint +
      this.subscription_id +
      '/batches/' +
      this.batch_id +
      '/upload_complete'
    }
    return this.http
      .patch(
        endpoint,
        {
          status: true,
        },
        {
          reportProgress: true,
          observe: 'events',
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   *
   * @param file
   * @param moduleId
   * @returns the response after file uploaded/ file upload failed
   */
  uploadData = (
    file,
    subscription_id,
    name,
    scBatchId,
    inputTempId,
    outputTempId,
    description,
    reference
  ) => {
    this.file = file;
    this.startSize = 0;
    this.endSize = 0;
    this.totalSent = 0;
    this.subscription_id = subscription_id;
    return this.getSignedUrl(
      file,
      subscription_id,
      name,
      scBatchId,
      inputTempId,
      outputTempId,
      description,
      reference
    ).pipe(
      mergeMap((resp) => {
        // console.log(resp.headers.get('location'));
        this.sessionUri = resp.headers.get('location');
        // console.log(this.sessionUri);
        return this.createAndUploadChunk(file, this.sessionUri);
      }),
      concatMap((r) => r),
      map(this.updateProgress),
      catchError((err) => throwError(err))
    );
  };

  /**
   *
   * @param file
   * @param sessionUri
   * @returns chunks of file|sends chunks to server
   */
  createAndUploadChunk = (file: File, sessionUri: string): Observable<any> => {
    const chunks = [];
    let totalFileSize = file.size;
    let contentType = file.type;
    this.totalChunk = Math.ceil(totalFileSize / this.chunkSize);
    let headers;
    while (totalFileSize > this.endSize) {
      // console.log('while');
      let slice: Blob;
      let contentRangeHeader: string;
      this.endSize = this.startSize + this.chunkSize;
      // for file size bigger than chunk size
      if (totalFileSize > this.chunkSize) {
        if (totalFileSize - this.endSize < 0) {
          this.endSize = totalFileSize;
          slice = file.slice(this.startSize, this.endSize, contentType);
          contentRangeHeader = `bytes ${this.startSize}-${this.endSize}/*`;
        } else {
          slice = file.slice(this.startSize, this.endSize + 1, contentType);
          contentRangeHeader = `bytes ${this.startSize}-${this.endSize}/${file.size}`;
        }
        headers = new HttpHeaders({
          'Content-Range': contentRangeHeader,
          'Chunk-Size': this.chunkSize.toString(),
        });
      } else {
        // for file size smaller than chunk size
        // no need to send content range
        slice = file.slice(this.startSize, totalFileSize, contentType);
        headers = new HttpHeaders({
          'Chunk-Size': this.chunkSize.toString(),
        });
      }
      chunks.push(
        this.http
          .put(sessionUri, slice, {
            headers: headers,
          })
          .pipe(
            map((resp: any) => {}),
            catchError((err: HttpErrorResponse) => {
              if (err.status === 308) {
                return of(undefined);
              }
              return throwError(err);
            })
          )
      );
      this.startSize = this.endSize;
    }
    return concat(chunks);
  };

  /**
   *
   * @returns resumes file uploaded in case upload was interrupted
   */
  resumeFileUpload = (): Observable<any> => {
    return this.http
      .put(this.sessionUri, '', {
        headers: new HttpHeaders({
          'Content-Range': `bytes */${this.file.size}`,
        }),
      })
      .pipe(
        catchError((err: HttpErrorResponse) => {
          if (err.status === 308) {
            return of(parseInt(err.headers.get('range').split('-')[1], 10));
          }
          this.updateServer({ status: 'failed' });
          return throwError(err);
        }),
        mergeMap((startFrom: number) => {
          this.startSize = startFrom + 1;
          this.endSize = this.startSize + this.chunkSize;
          return this.createAndUploadChunk(this.file, this.sessionUri);
        }),
        concatMap((r) => r),
        map(this.updateProgress),
        catchError((err) => throwError(err))
      );
  };
}
