<!-- side navigation -->
<div class="sidebar">
  <div class="sidebar__nav" fxLayout="column">
    <ul fxLayout="column" fxLayoutGap="5px">
      <!-- nav list -->
      <li matRipple>
        <a
          fxLayout="column"
          fxLayoutAlign="space-between center"
          i18n-matTooltip
          matTooltip="View uploaded batches and monitor their progress"
          matTooltipPosition="right"
          [routerLink]="['/home']" [queryParams]="{ sub: subscriptionId }"
        >
          <div
            class="menu-wrapper"
            fxLayout="column"
            fxLayoutAlign="space-between center"
          >
            <div
              class="menu-title"
              fxLayoutAlign="center center"
              routerLinkActive="active"
              #home="routerLinkActive"
            >
              <img
                [src]="
                home.isActive
                    ? 'assets/images/side-nav-svg/home-active.svg'
                    : 'assets/images/side-nav-svg/home.svg'
                "
              />
            </div>
            <span class="menu-text" routerLinkActive="active-text" i18n>Home</span>
          </div>
        </a>
      </li>
      <li>
        <a
          fxLayout="column"
          fxLayoutAlign="space-between center"
          [routerLink]="['/review']" [queryParams]="{ sub: subscriptionId, bucket: 'IN_PROGRESS' }"
          i18n-matTooltip
          matTooltip="Review content at SKU level."
          matTooltipPosition="right"
        >
          <div
            class="menu-wrapper"
            fxLayout="column"
            fxLayoutAlign="space-between center"
          >
            <div
              class="menu-title"
              fxLayoutAlign="center center"
              routerLinkActive="active"
              #review="routerLinkActive"
            >
              <img
                [src]="
                  review.isActive
                    ? 'assets/images/side-nav-svg/review-active.svg'
                    : 'assets/images/side-nav-svg/review.svg'
                "
              />
            </div>
            <span class="menu-text" routerLinkActive="active-text" i18n
              >Review</span
            >
          </div>
        </a>
      </li>
      <li>
        <a
          fxLayout="column"
          fxLayoutAlign="space-between center"
          [routerLink]="['/products']" [queryParams]="{ sub: subscriptionId }"
          i18n-matTooltip
          matTooltip="Monitor the enrichment processes at SKU level."
          matTooltipPosition="right"
        >
          <div
            class="menu-wrapper"
            fxLayout="column"
            fxLayoutAlign="space-between center"
          >
            <div
              class="menu-title"
              fxLayoutAlign="center center"
              routerLinkActive="active"
              #products="routerLinkActive"
            >
              <img
                [src]="
                products.isActive
                    ? 'assets/images/side-nav-svg/product-active.svg'
                    : 'assets/images/side-nav-svg/products.svg'
                "
              />
            </div>
            <span class="menu-text" routerLinkActive="active-text" i18n
              >Products</span
            >
          </div>
        </a>
      </li>
      <li>
        <a
          fxLayout="column"
          fxLayoutAlign="space-between center"
          [routerLink]="['/settings']" [queryParams]="{ sub: subscriptionId }"
          i18n-matTooltip
          matTooltip="Set API usage."
          matTooltipPosition="right"
        >
          <div
            class="menu-wrapper"
            fxLayout="column"
            fxLayoutAlign="space-between center"
          >
            <div
              class="menu-title"
              fxLayoutAlign="center center"
              routerLinkActive="active"
              #settings="routerLinkActive"
            >
              <img
                [src]="
                  settings.isActive
                    ? 'assets/images/side-nav-svg/settings-active.svg'
                    : 'assets/images/side-nav-svg/Settings.svg'
                "
              />
            </div>
            <span class="menu-text" routerLinkActive="active-text" i18n>Settings</span>
          </div>
        </a>
      </li>
    </ul>

    <!-- <div fxLayout="column" class="support-icon">
      <ul>
        <li>
          <a
            fxLayout="column"
            fxLayoutAlign="space-between center"
            [routerLink]="['/help']" [queryParams]="{ sub: subscriptionId }"
          >
            <div
              class="menu-wrapper"
              fxLayout="column"
              fxLayoutAlign="space-between center"
            >
              <div
                class="menu-title"
                fxLayoutAlign="center center"
                routerLinkActive="active"
                #help="routerLinkActive"
              >
                <img
                  [src]="
                  help.isActive
                      ? 'assets/images/side-nav-svg/help-white.svg'
                      : 'assets/images/side-nav-svg/help.svg'
                  "
                />
              </div>
              <span class="menu-text" routerLinkActive="active-text" i18n>Help</span>
            </div>
          </a>
        </li>
      </ul>
    </div> -->
  </div>
</div>
