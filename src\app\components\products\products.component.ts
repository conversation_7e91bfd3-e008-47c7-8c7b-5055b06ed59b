import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { ProductsService } from 'src/app/services/products.service';
import { ReviewService } from 'src/app/services/review.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpErrorResponse } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import moment from 'moment';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { Subscription, timer } from 'rxjs';
import { UserService } from 'src/app/services/user.service';
import { UndoSnackbarComponent } from '../../_dialogs/undo-snackbar/undo-snackbar.component';
import { SnackbarService } from '../../services/snackbar.service';
import { UnderscoreAsSpacePipe } from 'src/app/_pipe/underscore-as-space.pipe';

@Component({
  selector: 'app-products',
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss'],
})
export class ProductsComponent implements OnInit {
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: string;
  customStartDate: string;
  maxDate: Date;
  minDate: Date;
  pageNumber;
  selected = 'recent';
  start_date;
  end_date;
  page: number = 1;
  size: number = 50;
  tableDataLoading: boolean;
  totalItems;
  subscriptionId;
  batch_id;
  bucketFromUrl;
  productHeaderData;
  displayedColumns;
  dataSource;
  productTableData: any[] = [];
  selectedBucketSlug = 'IN_QUEUE';
  selectedIndex;
  selectedIndexAfterBucketUpdate;
  bucket;
  searchedItems: string[] = [];
  getBatchProgressTimer: Subscription;
  selectedProducts = {};
  permissionsObject;
  tabIndexWithData;
  tabs: any[] = [
    // { name: 'In Queue', value: 'IN_QUEUE', slug: 'in_queue' },
    {
      name: $localize`:bucket:In Progress`,
      value: 'IN_PROGRESS',
      slug: 'in_progress',
      desc: $localize`:bucket description:All SKUs that are undergoing enrichment`,
    },
    {
      name: $localize`:bucket:Accepted`,
      value: 'ACCEPTED',
      slug: 'accepted',
      desc: $localize`:bucket description:All SKUs enriched by dataX with high confidence and don't need to be reviewed. You can still choose to make minor edits and add comments here. Possible actions: Leave as 'Accepted' / Move to 'Review' / Move to 'Rework'`,
    },
    {
      name: $localize`:bucket:Review`,
      value: 'REVIEW',
      slug: 'review',
      desc: $localize`:bucket description:SKUs that have undergone enrichment, but dataX has less confidence in, and requires your review. You can make minor edits and add comments here. Possible actions: Move to 'Accepted' / Move to 'Rework'`,
    },
    {
      name: $localize`:bucket:Rework`,
      value: 'REWORK',
      slug: 'rework',
      desc: $localize`:bucket description:SKUs that need to be enriched again. Post-enrichment, the SKUs appear in the 'Accepted' bucket`,
    },
    {
      name: $localize`:bucket:Insufficient Data`,
      value: 'INSUFFICIENT_DATA',
      slug: 'insufficient_data',
      desc: $localize`:bucket description:SKUs that cannot be enriched because of missing data`,
    },
    // {
    //   name: $localize`:bucket:Duplicate`,
    //   value: 'DUPLICATE',
    //   slug: 'duplicate',
    //   desc: $localize`:bucket description:Duplicate SKUs`,
    // },
    // {
    //   name: $localize`:bucket:Other`,
    //   value: 'OTHER',
    //   slug: 'other',
    //   desc: $localize`:bucket description:SKUs that cannot be enriched for other reasons`,
    // },
  ];
  filterList: [];
  bucketWiseCount;
  search;
  selectable = true;
  removable = true;
  addOnBlur = true;
  searchedMultipleVals: string[] = [];
  filterObj: Object = {};
  datePickerValue;
  productFilterList;
  productPageFilters;
  userViewPreference;
  readonly separatorKeysCodes: number[] = [ENTER, COMMA];
  allTabData = {};
  fetchTableData: Subscription;
  actionFromUrl: string;
  constructor(
    private productService: ProductsService,
    private reviewService: ReviewService,
    private matSnackbar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute,
    private userService: UserService,
    private snackbarService: SnackbarService
  ) {}

  @ViewChild(MatPaginator) paginator: MatPaginator;

  ngOnInit() {
    // add subscription id from local storage in url as param
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.batch_id = this.route.snapshot.queryParams['batch_id'];
    this.bucketFromUrl = this.route.snapshot.queryParams['bucket'];
    this.actionFromUrl = this.route.snapshot.queryParams['action'];
    const currentYear = new Date().getFullYear();
    this.minDate = new Date(currentYear - 20, 0, 1);
    this.maxDate = new Date();
    this.currentDate = moment().format('YYYY-MM-DD');
    // date picker options
    this.datePickerOptions = [
      {
        display: 'Recent',
        value: 'recent',
        start_date: '',
      },
      {
        display: $localize`Last Week`,
        value: 'last_week',
        start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
      },
      {
        display: $localize`Last Month`,
        value: 'last_month',
        start_date: moment()
          .subtract(1, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: $localize`Last Quarter`,
        value: 'last_quarter',
        start_date: moment()
          .subtract(3, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        value: 'custom_range',
      },
    ];
    if (localStorage.getItem('productsFilterObj'))
      this.productPageFilters = JSON.parse(
        localStorage.getItem('productsFilterObj')
      );
    if (
      this.productPageFilters &&
      this.productPageFilters != 'undefined' &&
      JSON.parse(localStorage.productsFilterObj).hasOwnProperty(
        this.subscriptionId
      ) && this.actionFromUrl == 'back'
    ) {
      // retrieve all filter values
      this.productPageFilters[this.subscriptionId].q.forEach((element) => {
        this.searchedItems.push(element);
      });
      this.datePickerValue = this.productPageFilters[this.subscriptionId]
        ?.date_picker_value
        ? this.productPageFilters[this.subscriptionId].date_picker_value
        : 'recent';
      this.selected = this.datePickerValue;
      this.selectedProducts = this.productPageFilters[this.subscriptionId]
        ?.product_list_model
        ? this.productPageFilters[this.subscriptionId].product_list_model
        : {};
      this.start_date = this.productPageFilters[this.subscriptionId]?.start_date
        ? this.productPageFilters[this.subscriptionId]?.start_date
        : '';
      this.end_date = this.productPageFilters[this.subscriptionId]?.end_date
        ? this.productPageFilters[this.subscriptionId]?.end_date
        : '';
      this.searchedMultipleVals = this.productPageFilters[this.subscriptionId]
        ?.product_list
        ? this.productPageFilters[this.subscriptionId].product_list
        : [];
        this.page = this.productPageFilters[this.subscriptionId]
        ?.page
        this.size = this.productPageFilters[this.subscriptionId]
        ?.size;
      //  this.selectedProducts = this.reviewPageFilters? this.reviewPageFilters[this.subscriptionId].product_list : [];
    } else {
      this.start_date = '';
      this.end_date = '';
      this.searchedMultipleVals = [];
      this.searchedItems = [];
      this.selected = 'recent';
    }

    // see if url has bucket available
    if (this.bucketFromUrl) {
      this.bucket = this.bucketFromUrl.toUpperCase();
      this.selectedIndex = this.tabs.findIndex(
        (tab) => tab.value == this.bucket
      );
    } else {
      this.bucket = this.tabs[0].value;
    }
    // push batch Id from URL in searched items array
    // ignore whitespaces
    if (
      this.batch_id &&
      this.batch_id != '' &&
      this.searchedItems.indexOf(this.batch_id.trim()) < 0
    ) {
      this.searchedItems.push(this.batch_id.trim());
      // create an object to be saved in local storage
      this.filterObj[this.subscriptionId] = {
        q: this.searchedItems,
        date_picker_value: this.datePickerValue,
        start_date: this.start_date,
        end_date: this.end_date,
        product_list_model: this.selectedProducts,
        product_list: this.searchedMultipleVals,
        page: this.page,
        size: this.size,
      };
      localStorage.setItem('productsFilterObj', JSON.stringify(this.filterObj));
    }
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    // this.getProductTable(
    //   this.subscriptionId,
    //   this.page,
    //   this.size,
    //   this.bucket,
    //   this.searchedItems,
    //   this.searchedMultipleVals,
    //   this.start_date,
    //   this.end_date
    // );
    // get product filter options
    this.getProductFilterList(this.subscriptionId);
    // get product table headers
    this.getProductTableHeaders(this.subscriptionId);
    // call batch progress API to segregate and style only Batch IDs from filters
    // timer polling every 10 sec to get updated progress
    const ti = timer(0, 30000);
    this.getBatchProgressTimer = ti.subscribe((t) => {
      this.getBatchProgress();
    });
    // retrieve product page permissionsObject
    this.permissionsObject = this.userService.appPermissions.bucket_permissions;
    // get product details view mode user preference stored in cookie
    if (this.userService.getUserPreferenceFromCookie()) {
      this.userViewPreference = this.userService.getUserPreferenceFromCookie();
    } else {
      this.userViewPreference = 'product-details';
    }
  }

  ngOnDestroy() {
    this.getBatchProgressTimer ? this.getBatchProgressTimer.unsubscribe() : '';
    this.fetchTableData.unsubscribe();
  }

  /**
   * get options for filters
   * @param subscription_id
   */
  getProductFilterList = (subscription_id) => {
    this.reviewService.getReviewFilterList(subscription_id, false).subscribe({
      next: (resp) => {
        this.productFilterList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
  };

  /**
   * get Date
   * @param range
   * @returns
   */
  getDataForDate = (range: string) => {
    // do nothing for custom range
    if (this.selected == 'custom_range') {
      return null;
    }
    (this.customStartDate = ''), (this.customEndDate = '');
    // for others
    let interval = this.datePickerOptions.filter((item) => {
      return item.value === range;
    })[0];
    this.start_date = interval['start_date'];
    if (this.selected == 'recent') {
      this.end_date = '';
    } else {
      this.end_date = this.currentDate;
    }
    this.page = 1;
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list: this.searchedMultipleVals,
      product_list_model: this.selectedProducts,
      date_picker_value: this.datePickerValue,
      page: this.page,
      size: this.size,
    };
    localStorage.setItem('productsFilterObj', JSON.stringify(this.filterObj));
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    // this.getProductTable(
    //   this.subscriptionId,
    //   this.page,
    //   this.size,
    //   this.bucket,
    //   this.searchedItems,
    //   this.searchedMultipleVals,
    //   this.start_date,
    //   this.end_date
    // );
  };

  ed;
  sd;
  /**
   * set date range
   * @param dateRangeStart
   * @param dateRangeEnd
   */
  dateRangeChange = (
    dateRangeStart: HTMLInputElement,
    dateRangeEnd: HTMLInputElement
  ) => {
    if (
      moment(dateRangeStart?.value, 'DD-MM-YYYY').isValid() &&
      moment(dateRangeEnd?.value, 'DD-MM-YYYY').isValid()
    ) {
      this.selected = 'custom_range';
      this.sd = moment(dateRangeStart?.value, 'DD-MM-YYYY').format(
        'YYYY-MM-DD'
      );
      this.ed = moment(dateRangeEnd?.value, 'DD-MM-YYYY').format('YYYY-MM-DD');
      this.page = 1;
      if (this.selected == 'custom_range') {
        this.start_date = this.sd;
        this.end_date = this.ed;
      }
    }
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list: this.searchedMultipleVals,
      product_list_model: this.selectedProducts,
      date_picker_value: this.datePickerValue,
      page: this.page,
      size: this.size,
    };
    localStorage.setItem('productsFilterObj', JSON.stringify(this.filterObj));
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    // this.getProductTable(
    //   this.subscriptionId,
    //   this.page,
    //   this.size,
    //   this.bucket,
    //   this.searchedItems,
    //   this.searchedMultipleVals,
    //   this.start_date,
    //   this.end_date
    // );
  };

  /**
   * get filters on selection
   */
  getProductSelection = () => {
    this.searchedMultipleVals = [];
    this.selectedProducts;
    for (const property in this.selectedProducts) {
      if (this.selectedProducts[property].length) {
        this.selectedProducts[property].forEach((element) => {
          this.searchedMultipleVals.push(element);
        });
      }
    }
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
      page: this.page,
      size: this.size,
    };
    this.page = 1;
    localStorage.setItem('productsFilterObj', JSON.stringify(this.filterObj));
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    // this.getProductTable(
    //   this.subscriptionId,
    //   this.page,
    //   this.size,
    //   this.bucket,
    //   this.searchedItems,
    //   this.searchedMultipleVals,
    //   this.start_date,
    //   this.end_date
    // );
  };

  storePageAndSize = () => {
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
      page: this.page,
      size: this.size,
    };
    localStorage.setItem('productsFilterObj', JSON.stringify(this.filterObj));
  };

  /**
   * Called when tabs changed
   * @param tabChangeEvent
   */
  bucketTabChange = (tabChangeEvent): void => {
    console.log('tab changed');
    this.selectedBucketSlug = tabChangeEvent.tab.textLabel;
    // priority given to tab after bucket update
    this.bucket = this.selectedIndexAfterBucketUpdate
      ? this.tabs[this.selectedIndexAfterBucketUpdate].value
      : tabChangeEvent.tab.textLabel.toUpperCase();
    this.selectedIndex = this.selectedIndexAfterBucketUpdate
      ? this.selectedIndexAfterBucketUpdate
      : tabChangeEvent.index;
    this.tableDataLoading = true;
    this.page = 1;
    // updating query param as per selected tab name
    this.router.navigate([], {
      queryParams: {
        bucket: this.bucket,
      },
      queryParamsHandling: 'merge',
    });
    // get product table data
    this.getProductTable(
      this.subscriptionId,
      this.page,
      this.size,
      this.bucket,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * get bucket wise count
   * @param subs_id
   */
  getBucketWiseCount = (
    subs_id,
    searchedItems,
    filters,
    start_date,
    end_date
  ) => {
    console.log(this.bucket);
    let oldBucket = this.bucket;
    this.tableDataLoading = true;
    this.productService
      .getBucketCount(subs_id, searchedItems, filters, start_date, end_date)
      .subscribe({
        next: (resp) => {
          this.bucketWiseCount = resp.result;
          // this.bucketWiseCount = {
          //   accepted: 0,
          //   in_progress: 0,
          //   insufficient_data: 20,
          //   review: 110,
          //   rework: 0,
          // };
          if (!this.bucketFromUrl) {
            // go to the first tab which has the data
            this.tabIndexWithData = this.tabs.findIndex(
              (tab) => this.bucketWiseCount[tab.slug] > 0
            );
            // Set selectedIndex to the found index if it exists, otherwise set it to 0
            this.selectedIndex =
              this.tabIndexWithData > -1 ? this.tabIndexWithData : 0;
            // Set the bucket to the value of the selected tab
            this.bucket = this.tabs[this.selectedIndex].value;
          }

          this.getProductTable(
            this.subscriptionId,
            this.page,
            this.size,
            this.bucket,
            this.searchedItems,
            this.searchedMultipleVals,
            this.start_date,
            this.end_date
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * Adds search query to the list on enter
   * @param event
   */

  addQuery = (searchVal) => {
    if (
      searchVal &&
      searchVal != '' &&
      this.searchedItems.indexOf(searchVal.trim()) < 0
    ) {
      //
      this.searchedItems.push(searchVal.trim());
      // call batch progress API to segregate and style only Batch IDs from filters
      this.getBatchProgress();
      console.log(this.searchedItems)
      this.filterObj[this.subscriptionId] = {
        q: this.searchedItems,
        start_date: this.start_date,
        end_date: this.end_date,
        product_list_model: this.selectedProducts,
        product_list: this.searchedMultipleVals,
        date_picker_value: this.datePickerValue,
        page: this.page,
        size: this.size,
      };
      console.log(this.filterObj)
      localStorage.setItem('productsFilterObj', JSON.stringify(this.filterObj));
      this.getBucketWiseCount(
        this.subscriptionId,
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date
      );
      // this.getProductTable(
      //   this.subscriptionId,
      //   this.page,
      //   this.size,
      //   this.bucket,
      //   this.searchedItems,
      //   this.searchedMultipleVals,
      //   this.start_date,
      //   this.end_date
      // );
    }
    this.search = '';
  };

  /**
   * Removes search query from filter list
   * @param item
   */
  removeQuery = (item) => {
    // console.log(this.bucket);
    const index = this.searchedItems.indexOf(item.value.trim());
    this.searchedItems.splice(index, 1);
    this.getBatchProgress();
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
      page: this.page,
      size: this.size,
    };
    localStorage.setItem('productsFilterObj', JSON.stringify(this.filterObj));
    // remove respective query param from url
    this.router.navigate([], {
      queryParams: {
        batch_id: null,
      },
      queryParamsHandling: 'merge',
    });
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    // this.getProductTable(
    //   this.subscriptionId,
    //   this.page,
    //   this.size,
    //   this.bucket,
    //   this.searchedItems,
    //   this.searchedMultipleVals,
    //   this.start_date,
    //   this.end_date
    // );
  };

  /**
   * Get is_batch boolean and progress for each filter
   */
  getBatchProgress = () => {
    this.productService
      .getBatchProgress(this.subscriptionId, this.searchedItems)
      .subscribe({
        next: (resp) => {
          this.filterList = resp.result;
        },
        error: (err: HttpErrorResponse) => {
          // this.matSnackbar.open(`${err.error.detail}`, 'OK', {
          //   duration: 3000,
          // });
          this.snackbarService.openSnackBar(err.error, 'OK');
        },
      });
  };

  /***
   * decode query before displaying in chip
   */
  decode(content: string) {
    return decodeURIComponent(content);
  }

  /**
   * get product table headers
   * @param subs_id
   */
  getProductTableHeaders = (subs_id) => {
    this.productService.getProductHeader(subs_id).subscribe({
      next: (resp) => {
        this.productHeaderData = resp;
        //
        // console.log('productHeaderData: ', this.productHeaderData);
        //
        this.productHeaderData.push({
          slug: 'actions',
          display_name: $localize`Actions`,
        });
        if (this.productHeaderData) {
          this.displayedColumns = this.productHeaderData.map(
            (column) => column.slug
          );
        }
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
  };

  /**
   * get product table rows
   * @param subs_id
   * @param page
   * @param size
   * @param bucket
   * @param search
   * @param start_date
   * @param end_date
   */
  getProductTable = (
    subs_id,
    page,
    size,
    bucket,
    search_query,
    filter,
    start_date,
    end_date
  ) => {
    this.tableDataLoading = true;
    // show empty table & loader
    this.productTableData = [];
    this.dataSource = new MatTableDataSource(this.productTableData);
    this.fetchTableData && this.fetchTableData.unsubscribe(); // Unsubscribe if any prev api call active
    // populate table data
    this.fetchTableData = this.productService
      .getProductList(
        subs_id,
        page,
        size,
        bucket,
        search_query,
        filter,
        start_date,
        end_date
      )
      .subscribe({
        next: (resp) => {
          this.tableDataLoading = false;
          this.productTableData = resp.result;
          this.totalItems = resp.total_items;
          this.size = resp.page_size;
          this.page = resp.page;
          this.dataSource = new MatTableDataSource(this.productTableData);
          // nullify selectedIndexAfterBucketUpdate
          this.selectedIndexAfterBucketUpdate = null;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.tableDataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * Get table data on pagination
   * @param identifier
   * @param tabName
   */
  onPaginateChange = (identifier) => {
    this.page = identifier.pageIndex + 1;
    this.size = identifier.pageSize;
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
      page: this.page,
      size: this.size,
    };
    localStorage.setItem('productsFilterObj', JSON.stringify(this.filterObj));
    this.getProductTable(
      this.subscriptionId,
      this.page,
      this.size,
      this.bucket,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * reset filter
   */
  resetFilters = () => {
    this.search = '';
    this.selected = 'recent';
    this.datePickerValue = 'recent';
    this.searchedItems = [];
    this.searchedMultipleVals = [];
    this.filterObj = {};
    this.start_date = '';
    this.end_date = '';
    this.selectedProducts = {};
    this.selectedIndex = 0;
    localStorage.removeItem('productsFilterObj');
    this.customStartDate = '';
    this.customEndDate = '';
    this.getProductFilterList(this.subscriptionId);
    this.filterList = [];
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { sub: this.subscriptionId },
    });
    this.page = 1;
    this.size = 50;
    // this.getProductTable(
    //   this.subscriptionId,
    //   this.page,
    //   this.size,
    //   this.bucket,
    //   this.searchedItems,
    //   this.searchedMultipleVals,
    //   this.start_date,
    //   this.end_date
    // );
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * To update current bucket to new bucket
   * @param rowId
   * @param bucketValue
   */
  // bucketCheck;
  updateBucket = (rowId, bucketValue) => {
    // console.log(bucketValue)
    const retrieveBucket = this.bucket.toLowerCase();

    const obj = { bucket: bucketValue };
    // console.log(obj)
    this.productService
      .bucketUpdate(rowId, obj, this.subscriptionId)
      .subscribe({
        next: (resp) => {
          // switch tab to go to updated bucket
          // console.log(bucketValue, this.bucketCheck)
          this.selectConcernedTab(bucketValue.toLowerCase());
          // opens undo snackbar component
          this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
            duration: 30000,
            // passing args as data
            data: {
              response: resp.detail,
              module: this.subscriptionId,
              Id: rowId,
              onUndo: () => {
                this.selectConcernedTab(retrieveBucket);
              },
            },
          });
          // }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * Takes the focus to concerned tab
   * @param bucket
   */
  selectConcernedTab = (bucket) => {
    console.log(bucket)
    this.dataSource = [];
    this.bucket = bucket.toUpperCase();
    let tabIndex = this.tabs.findIndex((tab) => tab.slug == bucket);
    console.log(bucket, tabIndex);
    this.selectedIndex = tabIndex;
    this.selectedIndexAfterBucketUpdate = tabIndex;
    this.getBucketWiseCount(
      this.subscriptionId,
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date // updateTab param
    );
    // this.getProductTable(
    //   this.subscriptionId,
    //   this.page,
    //   this.size,
    //   this.bucket,
    //   this.searchedItems,
    //   this.searchedMultipleVals,
    //   this.start_date,
    //   this.end_date
    // );
  };
}
