import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root',
})
export class SnackbarService {
  constructor(private snackBar: MatSnackBar) {}

  openSnackBar = (message: any, action: string) => {
    console.log(message);
    if (message != 'login_required') {
      if (typeof message == 'string') {
        this.snackBar.open(message, action, {
          duration: 3000,
        });
      } else if (!message || !message.detail) {
        this.snackBar.open(
          `An unexpected error has occurred. Please contact dataX support.`,
          action,
          {
            duration: 3000,
          }
        );
      } else {
        this.snackBar.open(message.detail, action, {
          duration: 3000,
        });
      }
    }
  };
}
