import {
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ImageViewerDialogComponent } from '../../_dialogs/image-viewer-dialog/image-viewer-dialog.component';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ProductDetailsService } from '../../services/product-details.service';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { UndoSnackbarComponent } from '../../_dialogs/undo-snackbar/undo-snackbar.component';
import { CommentsService } from '../../services/comments.service';
import { UserService } from 'src/app/services/user.service';
import { ProductsService } from 'src/app/services/products.service';
import { QuillEditorComponent } from 'ngx-quill';
import { DOCUMENT } from '@angular/common';
import { FileUploadService } from 'src/app/services/file-upload.service';
import { SnackbarService } from '../../services/snackbar.service';
import { ConfirmDialog } from 'src/app/_dialogs/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-product-details',
  templateUrl: './product-details.component.html',
  styleUrls: ['./product-details.component.scss'],
})
export class ProductDetailsComponent implements OnInit {
  uploadCheck: boolean;
  uploadedFile: any;
  @HostListener('document:fullscreenchange', ['$event'])
  @HostListener('document:webkitfullscreenchange', ['$event'])
  @HostListener('document:mozfullscreenchange', ['$event'])
  @HostListener('document:MSFullscreenChange', ['$event'])
  fullScreenChange() {
    this.isFullscreen = !this.isFullscreen;
    this.isEditFullscreen = !this.isEditFullscreen;
  }
  @ViewChild(QuillEditorComponent) editor: QuillEditorComponent;
  @ViewChild('fullScreen') divRef: ElementRef;
  @ViewChild('fullScreenEdit') editFullScreen: ElementRef;

  /** editor ngx quill */

  content = '';
  matContent = '';
  selectedUsers: any[] = [];
  selectedUsersEdited: any[] = [];

  editorStyle = {
    maxHeight: '150px',
    height: '100px',
  };

  modules = {
    mention: {
      allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
      onSelect: (item, insertItem) => {
        const editor = this.editor.quillEditor;
        this.selectedUsers.push(item.id);
        insertItem(item);
        // necessary because quill-mention triggers changes as 'api' instead of 'user'
        editor.insertText(editor.getLength() - 1, '', 'user');
      },
      source: (searchTerm, renderList) => {
        const values = this.mentionUsers;

        if (searchTerm.length === 0) {
          renderList(values, searchTerm);
        } else {
          const matches = [];

          values.forEach((entry) => {
            if (
              entry.value.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1
            ) {
              matches.push(entry);
            }
          });
          renderList(matches, searchTerm);
        }
      },
    },
  };
  editModuleconfig = {
    mention: {
      allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
      onSelect: (item, insertItem) => {
        const editor = this.editor.quillEditor;
        // this.selectedUsersEdited.push(item.id);
        insertItem(item);
        // necessary because quill-mention triggers changes as 'api' instead of 'user'
        editor.insertText(editor.getLength() - 1, '', 'user');
      },
      source: (searchTerm, renderList) => {
        const values = this.mentionUsers;

        if (searchTerm.length === 0) {
          renderList(values, searchTerm);
        } else {
          const matches = [];

          values.forEach((entry) => {
            if (
              entry.value.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1
            ) {
              matches.push(entry);
            }
          });
          renderList(matches, searchTerm);
        }
      },
    },
  };
  isFullscreen: boolean = false;
  isEditFullscreen: boolean = false;
  files = [];
  currentInput;
  subscriptionId: any;
  row_id: any;
  bucket: any;
  search_queries;
  start_date;
  end_date;
  tableDataLoading: boolean = false;
  attributeLoading: boolean = false;
  tabList: any;
  myControl = new FormControl();
  autocompleteAttrSlug;
  optionsLoading: boolean;
  valArr: any = [];
  updatedVal: any = [];
  prediction_id: any;
  filteredOptions: Observable<any[]>;
  autoCompleteSuggestions: string[] = [];
  dataLoading: boolean = true;
  productImg: any;
  bucketVal;
  productBucket;
  productTitle;
  productDetailsData;
  productIds;
  previousPageUrl: any;
  filtersFromLocalStorage;
  nextRowId;
  previousRowId;
  product_list;
  permissionsObject;
  moveToBucketList: [];
  editorPlaceholder = $localize`Comment or mention others with @`;
  undoBucketValue;
  editorRefEdit;
  editorRef;
  isImgUpload: boolean = false;
  isImgUploadEdit: boolean = false;
  row: number;
  isAllowAttachment: boolean = false;
  filesEdit = [];
  isEditFileRemoved: boolean = false;
  editUploadcheck: boolean = false;
  attachmentID: number;
  @ViewChild('fileInput') imgFileInput;
  @ViewChild('fileInputEdit') imgFileInputEdit;
  dialogRef: MatDialogRef<ConfirmDialog, any>;
  isCommentChanged: boolean = false;

  constructor(
    @Inject(DOCUMENT) private document: any,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private productdetailsService: ProductDetailsService,
    public matSnackbar: MatSnackBar,
    private commentsService: CommentsService,
    private userService: UserService,
    private productService: ProductsService,
    private fileUploadService: FileUploadService,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit() {
    this.tableDataLoading = true;
    this.route.queryParams.subscribe((params: Params) => {
      this.row_id = params.row_id;
      this.bucket = params.bucket;

      //   if(this.bucket == undefined){
      //     this.bucket = params.from.toUpperCase();
      // }
    });
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    // console.log(this.previousPageUrl);
    this.previousPageUrl = this.route.snapshot.queryParams['from'];
    // retaining bucket for undo
    this.undoBucketValue = this.route.snapshot.queryParams['bucket'];
    // retrieve product page permissions
    // retrieve product page permissionsObject
    this.permissionsObject = this.userService.appPermissions.bucket_permissions;
    // get user data from local storage
    if (localStorage.getItem('user')) {
      this.userData = JSON.parse(localStorage.getItem('user'));
    }

    // apply filters based on origin
    if (this.previousPageUrl == 'products') {
      this.filtersFromLocalStorage = JSON.parse(
        localStorage.getItem('productsFilterObj')
      );
    } else if (this.previousPageUrl == 'review') {
      this.filtersFromLocalStorage = JSON.parse(
        localStorage.getItem('reviewFilterObj')
      );
    }
    if (this.filtersFromLocalStorage) {
      this.search_queries = this.filtersFromLocalStorage[this.subscriptionId].q;
      this.start_date =
        this.filtersFromLocalStorage[this.subscriptionId].start_date;
      this.end_date =
        this.filtersFromLocalStorage[this.subscriptionId].end_date;
      this.product_list =
        this.filtersFromLocalStorage[this.subscriptionId].product_list;
    }
    this.getProductDetailsData(this.row_id, this.bucket);
    this.getAttributeHeader();
    this.getUserNameList();
  }

  /**
   * get user list for tagging in comments
   */
  getUserNameList = () => {
    this.commentsService.getUserNamesToTag(this.subscriptionId).subscribe({
      next: (resp) => {
        this.mentionUsers = resp.result.map((obj) => ({
          id: obj.username,
          value: obj.name,
        }));
      },
    });
  };

  /**
   * Image  slider
   * @param productImg
   */
  openImageSlider(productImg) {
    this.dialog.open(ImageViewerDialogComponent, {
      data: { productImg },
    });
  }
  productModeChecked: boolean = true;
  /**
   * Toggle All settings
   * @param e
   */
  toggleSwitches = (e) => {
    if (e.checked) {
      this.productModeChecked = true;
    } else {
      this.route.queryParams.subscribe((params: Params) => {
        this.row_id = params.row_id;
        this.bucket = params.bucket;
      });
      this.router.navigate(['/details/review-mode'], {
        queryParams: {
          sub: this.subscriptionId,
          row_id: this.row_id,
          bucket: this.bucket,
          from: this.previousPageUrl,
        },
      });
    }
    this.setUserPreferenceInCookie(e.checked, 30);
  };

  /**
   * set user product details view mode preference
   * @param value
   * @param days
   */
  setUserPreferenceInCookie = (value, days) => {
    let view = value == true ? 'product-details' : 'review-mode';
    var expires = '';
    if (days) {
      var date = new Date();
      date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
      expires = '; expires=' + date.toUTCString();
    }
    this.document.cookie =
      'view_mode' + '=' + (view || '') + expires + '; path=/';
  };

  rowId;
  predictionId;
  predictionName;
  // bucketCheck;
  /**
   * prediction details
   */
  getProductDetailsData = (row_id, bucket) => {
    this.productdetailsService
      .getProductDetails(
        this.subscriptionId,
        row_id,
        bucket,
        this.search_queries,
        this.product_list,
        this.start_date,
        this.end_date
      )
      .subscribe({
        next: (resp) => {
          this.productDetailsData = resp.result;
          this.productTitle = this.productDetailsData?.title;
          this.productImg = this.productDetailsData?.images;
          this.productBucket = this.productDetailsData?.bucket.display_name;
          this.productIds = this.productDetailsData?.ids;
          this.nextRowId = this.productDetailsData.next_row;
          this.previousRowId = this.productDetailsData.previous_row;
          this.bucketVal = this.productDetailsData?.bucket.slug;
          this.predictionId = resp.result[0]?.predictions[0].prediction_id;
          this.predictionName = resp.result[0]?.predictions[0].display_name;

          this.moveToBucketList = this.permissionsObject[this.bucketVal];
          this.rowId = row_id;
          this.getCommentThread(
            this.subscriptionId,
            this.threadPage,
            this.threadSize,
            this.commentsListForType,
            this.rowId,
            this.searchInComment,
            true
          );

          this.tableDataLoading = false;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * To get side tab info
   * @param subscriptionId
   */
  getAttributeHeader = () => {
    this.productdetailsService.getAttributeList(this.subscriptionId).subscribe({
      next: (resp) => {
        this.tabList = resp.result;
        // this.tabList.forEach((data) => {
        //   this.tabKeys = data.slug;
        // });
      },
    });
  };

  update(event: KeyboardEvent) {
    event.preventDefault();
    this.updateFieldsOnClick();
  }
  /**
   * Updates  form field /drop down
   * @param predictionId
   * @param val
   */
  onChangeOfInput = (predictionId, val) => {
    this.valArr = [];
    this.valArr.push(val);
    this.updatedVal.push({
      prediction_id: predictionId,
      values: this.valArr,
    });
    this.valArr = [];
  };

  /**
   * update prediction
   */
  updateFieldsOnClick = () => {
    this.productdetailsService
      .patchEditedResponse(this.subscriptionId, this.row_id, this.updatedVal)
      .subscribe({
        next: (resp) => {
          if (resp.detail == 'success') {
            // undo snackbar
            this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
              duration: 30000,
              // passing args as data
              data: {
                response: resp.detail,
                module: this.subscriptionId,
                Id: this.row_id,
                onUndo: () => {
                  this.getProductDetailsData(this.row_id, this.bucket);
                },
              },
            });
            this.getProductDetailsData(this.row_id, this.bucket);
            this.updatedVal = [];
            this.valArr = [];
          }
          this.updatedVal = [];
          this.valArr = [];
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.getProductDetailsData(this.row_id, this.bucket);
          this.updatedVal = [];
          this.valArr = [];
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * go to previous product in queue
   * @param previousRowId
   */
  previousPage = (previousRowId) => {
    if (previousRowId === 'None') {
      this.matSnackbar.open(
        $localize`Please use Next Button to view more products`,
        'OK',
        {
          duration: 5000,
        }
      );
      return;
    } else {
      this.content = '';
      this.dataLoading = true;
      this.router.navigate([], {
        queryParams: {
          row_id: previousRowId,
          sub: this.subscriptionId,
          from: this.previousPageUrl,
          bucket: this.bucket,
        },
        preserveFragment: false,
        queryParamsHandling: '',
      });
      this.getAttributeHeader();
      this.getProductDetailsData(previousRowId, this.bucket);
      // this.getComments(previousRowId, this.module_slug);
    }
  };

  /**
   * go to next product in queue
   * @param nextRowId
   */
  nextPage = (nextRowId) => {
    // this.enableAddComment = false;
    if (nextRowId === 'None') {
      this.matSnackbar.open(
        $localize`You have viewed all the products in this batch`,
        $localize`OK`,
        {
          duration: 5000,
        }
      );
      return;
    } else {
      this.content = '';
      this.dataLoading = true;
      this.router.navigate([], {
        queryParams: {
          row_id: nextRowId,
          sub: this.subscriptionId,
          from: this.previousPageUrl,
          bucket: this.bucket,
        },
        preserveFragment: false,
        queryParamsHandling: '',
      });
      this.getAttributeHeader();
      this.getProductDetailsData(nextRowId, this.bucket);
      // this.getComments(nextRowId, this.module_slug);
    }
  };

  commentThread: any[] = [];
  threadPage: number = 1;
  threadSize: number = 10;
  threadTotalItems: number;
  threadTotalPage: number;
  commentsLoading: boolean;
  commentsListForType = 'row';
  userData;
  searchInComment = '';
  /**
   * event fired when comment thread section is scrolled
   * fetching new pages
   */
  onCommentThreadScroll = () => {
    // check if comment thread length is equal to total item capacity of pages
    // or check if total item size > total item capacity of pages
    // increment pageSize by 1
    if (this.threadTotalItems > this.commentThread.length) {
      if (this.commentThread.length == this.threadSize * this.threadPage) {
        this.threadPage++;
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.rowId,
          this.searchInComment,
          true
        );
      } else {
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.rowId,
          this.searchInComment,
          true
        );
      }
    }
  };

  /**
   * get comment thread for respective sku/batch comment
   * @param subs_id
   * @param page
   * @param size
   * @param category
   * @param batch_or_row_id
   * @param q
   * @param comment_thread
   */
  getCommentThread = (
    subs_id,
    page,
    size,
    category,
    batch_or_row_id,
    q,
    comment_thread
  ) => {
    this.commentsLoading = true;
    this.commentsService
      .getCommentsList(
        subs_id,
        page,
        size,
        category,
        batch_or_row_id,
        q,
        comment_thread
      )
      .subscribe({
        next: (resp) => {
          this.commentsLoading = false;
          this.threadPage = resp.page;
          this.threadSize = resp.page_size;
          this.threadTotalItems = resp.total_items;
          this.threadTotalPage = resp.total_pages;
          // if difference between total pages capacity and length of comment thread list is less than one page capacity
          // replace the last set of data with incoming data
          // using splice to replace
          if (
            this.threadPage * this.threadSize - this.commentThread.length <
            this.threadSize
          ) {
            this.commentThread.splice(
              (this.threadPage - 1) * this.threadSize,
              this.commentThread.length -
                (this.threadPage - 1) * this.threadSize,
              ...resp.result
            );
          } else {
            this.commentThread = [...this.commentThread, ...resp.result];
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.commentsLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };
  choices: User[] = [];
  mentionUsers: any[];
  taggedUsersByUsername: any[] = [];
  taggedUsersByName: any[] = [];
  taggedUsers: any[] = [];
  defSuggestionDisplayCount: number = 3;
  comments: any[] = [];
  activeCommentIndex = 1;
  /**
   * post comment
   * @param comment comment
   * @param i index
   */
  postComment = (comment, user, file, i?) => {
    let users = [];
    const IDs = [];

    let obj = {
      comment: comment,
      tagged_users: users,
      attachment_name: file.length > 0 ? file[0].name : file,
      file_hash_ids: IDs,
    };

    const { ops } = this.editor.quillEditor.getContents();
    const result = ops.reduce((acc, { insert }) => {
      if (typeof insert === 'string') {
      } else {
        if (insert.hasOwnProperty('mention')) {
          users.push(insert.mention.id);
        }
        if (insert.hasOwnProperty('image')) {
          const test = insert.image.match(/attachments\/(.*)\//);
          IDs.push(test[1]);
        }
      }

      return acc;
    }, '');

    // remove duplicate tagged users [allowed in editor]
    obj.tagged_users = [...new Set(users)];
    this.commentsService
      .postComment(
        this.subscriptionId,
        this.commentsListForType,
        this.rowId,
        obj
      )
      .subscribe({
        next: (resp) => {
          this.files = [];
          this.selectedUsers = [];
          this.content = '';
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          if (resp['signed_url'] === '') {
            this.getCommentThread(
              this.subscriptionId,
              (this.threadPage = 1),
              this.threadSize,
              this.commentsListForType,
              this.rowId,
              this.searchInComment,
              true
            );
            return;
          }
          this.fileUploadService
            .getSessionURI(resp.signed_url, file[0])
            .subscribe({
              next: (progress) => {
                this.getCommentThread(
                  this.subscriptionId,
                  (this.threadPage = 1),
                  this.threadSize,
                  this.commentsListForType,
                  this.rowId,
                  this.searchInComment,
                  true
                );
                this.fileUploadService
                  .uploadCompelte(true, resp.comment_id, this.subscriptionId)
                  .subscribe({
                    next: (resp) => {},
                  });
              },
              error: (HttpResponse: HttpErrorResponse) => {
                this.dataLoading = false;
                this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
              },
            });
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * update existing comment
   * @param comment
   */
  updateComment = (comment, user, file) => {
    const addedUsers = []; //only added users
    const IDs = [];
    let obj = {
      comment: comment['edited_text'],
      tagged_users: addedUsers, //default
      attachment_name: file.length > 0 ? file[0].name : file,
      remove_attachment_id: this.attachmentID,
      file_hash_ids: IDs,
      remove_tagged_users: [], // default
    };
    if (obj.comment == null) {
      obj.comment = '';
    }

    const { ops } = this.editorRefEdit.getContents();
    const holdEditorUsers = [];

    // check for tagged users
    const result = ops.reduce((acc, { insert }) => {
      if (typeof insert === 'string') {
      } else {
        if (insert.hasOwnProperty('mention')) {
          const existingUser = this.selectedUsersEdited.find(
            (that) => insert.mention.id === that.username
          );
          if (!existingUser) {
            addedUsers.push(insert.mention.id);
          }
          holdEditorUsers.push(insert.mention.id);
        }
        if (insert.hasOwnProperty('image')) {
          const test = insert.image.match(/attachments\/(.*)\//);
          IDs.push(test[1]);
        }
      }

      return acc;
    }, '');

    // check for removed users
    if (this.selectedUsersEdited.length > 0) {
      obj.remove_tagged_users = this.selectedUsersEdited
        .filter((each) => !holdEditorUsers.includes(each.username))
        .map((each) => each.username);
    }

    // remove duplicate tagged users [allowed in editor]
    obj.tagged_users = [...new Set(addedUsers)];
    this.commentsService
      .updateComment(this.subscriptionId, comment.comment_id, obj)
      .subscribe({
        next: (resp) => {
          this.filesEdit = [];
          this.selectedUsersEdited = [];
          this.attachmentID = null;
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          comment.editable = false;
          if (!resp['attachments'].hasOwnProperty('signed_url')) {
            this.updateSidepanelAndThread('edit');
            return;
          }
          if (
            resp['attachments'].hasOwnProperty('signed_url') &&
            resp['attachments']['signed_url'] != ''
          ) {
            this.updateSidepanelAndThread('edit');
            this.fileUploadService
              .getSessionURI(resp['attachments']['signed_url'], file[0])
              .subscribe({
                next: (progress) => {
                  // console.log(progress)
                  // this.updateSidepanelAndThread('edit');
                  this.fileUploadService
                    .uploadCompelte(
                      true,
                      comment.comment_id,
                      this.subscriptionId
                    )
                    .subscribe({
                      next: (resp) => {},
                    });
                },
                error: (HttpResponse: HttpErrorResponse) => {
                  this.dataLoading = false;
                  this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
                    duration: 3000,
                  });
                },
              });
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  updateSidepanelAndThread(mode?) {
    this.getCommentThread(
      this.subscriptionId,
      (this.threadPage = 1),
      this.threadSize,
      this.commentsListForType,
      this.rowId,
      this.searchInComment,
      true
    );
  }

  /**
   * edit comment
   * @param comment
   */
  editComment = (comment) => {
    this.isCommentChanged = false;
    this.attachmentID = null;
    this.selectedUsersEdited = comment.tagged_users;
    comment['edited_text'] = comment.text;
    this.isEditFileRemoved = false;
    this.filesEdit = [];
    this.editUploadcheck = false;
    this.isEditFileRemoved = false;
    comment['attachment'].hasOwnProperty('name')
      ? (this.isAllowAttachment = false)
      : (this.isAllowAttachment = true);
    // deactivate all other comment edits
    this.commentThread
      .filter((item) => item.comment_id != comment.comment_id)
      .forEach((row) => {
        row.editable = false;
      });
  };

  /**
   * delete comment from comment thread
   * @param comment_id
   * @param index
   */
  deleteComment = (comment_id, index) => {
    this.dialogRef = this.dialog.open(ConfirmDialog, {
      data: {
        content: $localize`Are you sure you want to delete this comment?`,
        status: 'DELETE COMMENTS',
        primarybtnName: $localize`Delete`,
        secondaryBtnName: $localize`Close`,
        trigger: (action) => {
          this.confirmDelete(comment_id, index);
        },
      },
    });
  };

  confirmDelete(comment_id, index) {
    this.dialogRef.afterClosed().subscribe((discard) => {
      if (discard) {
        this.commentsService
          .deleteComment(this.subscriptionId, comment_id)
          .subscribe({
            next: (resp) => {
              this.commentThread.splice(index, 1);
              this.snackbarService.openSnackBar(resp.detail, 'OK');
            },
            error: (HttpResponse: HttpErrorResponse) => {
              this.dataLoading = false;
              this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
            },
          });
      }
      this.dialogRef = null;
    });
  }

  /**
   * tag removal event in comment thread
   * @param e
   */
  taggedUserRemovedInCommentEdit = (e) => {};

  /**
   * used to highlight existing tagged users in textarea
   * @param comment
   * @returns
   */
  getSelectedChoices = (comment): User[] => {
    return comment.tagged_users;
  };

  previousBucket;
  /**
   * Update the form fields
   * @param rowId
   * @param bucketValue
   */
  updateBucket = (rowId, bucketValue) => {
    // console.log(bucketValue, 'bucket');
    this.previousBucket = this.bucket;
    let obj = { bucket: bucketValue.toUpperCase() };
    this.productService
      .bucketUpdate(rowId, obj, this.subscriptionId)
      .subscribe({
        next: (resp) => {
          let urlTree = this.router.parseUrl(this.router.url);
          urlTree.queryParams['bucket'] = bucketValue.toUpperCase();
          this.router.navigateByUrl(urlTree);
          // refresh data
          this.getProductDetailsData(this.row_id, bucketValue.toUpperCase());
          // Undo snackbar
          this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
            duration: 30000,
            // passing args as data
            data: {
              response: resp.detail,
              module: this.subscriptionId,
              Id: rowId,
              onUndo: () => {
                // modify url with undo bucket
                let urlTree = this.router.parseUrl(this.router.url);
                urlTree.queryParams['bucket'] =
                  this.undoBucketValue.toUpperCase();
                this.router.navigateByUrl(urlTree);

                this.getProductDetailsData(this.row_id, this.undoBucketValue);
              },
            },
          });
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  onFileSelected = (event, mode) => {
    if (
      event.target.files.length > 0 &&
      event.target.files[0].size <= 10000000
    ) {
      if (mode === 'create') {
        this.uploadCheck = true;
        this.uploadedFile = event.target.files[0];
        this.files.push(this.uploadedFile);
      }

      if (mode === 'edit') {
        this.editUploadcheck = true;
        this.filesEdit.push(event.target.files[0]);
        this.isAllowAttachment = false;
        this.isCommentChanged = true;
      }
    } else {
      this.matSnackbar.open('please upload file size less than 10MB', 'OK', {
        duration: 3000,
      });
    }
  };

  toggleFullscreen(isActive, mode) {
    isActive ? this.openFullscreen(mode) : this.closeFullscreen();
  }

  openFullscreen(mode) {
    let elem;
    if (mode && mode === 'create') {
      elem = this.divRef.nativeElement;
    }
    if (mode && mode === 'edit') {
      elem = this.editFullScreen.nativeElement;
    }

    if (elem.requestFullscreen) {
      elem.requestFullscreen();
    } else if (elem.msRequestFullscreen) {
      /* IE/Edge */
      elem.msRequestFullscreen();
    } else if (elem.mozRequestFullScreen) {
      /* Firefox */
      elem.mozRequestFullScreen();
    } else if (elem.webkitRequestFullscreen) {
      /* Chrome, Safari and Opera */
      elem.webkitRequestFullscreen();
    }
  }

  closeFullscreen() {
    if (this.document.exitFullscreen) {
      this.document.exitFullscreen();
    } else if (this.document.mozCancelFullScreen) {
      /* Firefox */
      this.document.mozCancelFullScreen();
    } else if (this.document.webkitExitFullscreen) {
      /* Chrome, Safari and Opera */
      this.document.webkitExitFullscreen();
    } else if (this.document.msExitFullscreen) {
      /* IE/Edge */
      this.document.msExitFullscreen();
    }
  }

  handler(mode, comment?) {
    if (mode === 'create') {
      this.postComment(this.content, this.selectedUsers, this.files);
    }
    if (mode === 'edit') {
      this.updateComment(comment, this.selectedUsersEdited, this.filesEdit);
    }
  }

  clear = (index) => {
    this.uploadCheck = false;
    this.files.splice(index, 1);
    // this.files = this.files;
  };

  clearEditAttachment(comment, index) {
    this.filesEdit = [];
    this.isEditFileRemoved = true;
    this.isAllowAttachment = true;
    this.isCommentChanged = false;
    this.attachmentID = comment['attachment']['attachment_id'];
  }

  getEditorInstance(editorInstance: any, mode?) {
    if (mode && mode === 'edit') {
      this.editorRefEdit = editorInstance;
      this.attachPasteListener('edit');
    } else {
      this.editorRef = editorInstance;
      this.attachPasteListener('create');
    }
  }

  attachPasteListener(mode) {
    if (mode === 'create') {
      this.editorRef.root.addEventListener('paste', (e) => {
        const clipboardData = e.clipboardData || (<any>window).clipboardData;
        // const isImage = clipboardData.types.length && clipboardData.types.join('').includes('Files');
        if (clipboardData.files.length === 0) {
          return;
        } else if (clipboardData.files[0].type.startsWith('image/')) {
          e.preventDefault();
          const file = clipboardData.files[0];
          this.insertImage('', 'copy', file);
        }
      });
    }
    if (mode === 'edit') {
      this.editorRefEdit.root.addEventListener('paste', (e) => {
        const clipboardData = e.clipboardData || (<any>window).clipboardData;
        if (clipboardData.files.length === 0) {
          return;
        } else if (clipboardData.files[0].type.startsWith('image/')) {
          e.preventDefault();
          const file = clipboardData.files[0];
          this.insertImage('', 'edit', file);
        }
      });
    }
  }

  insertImage(event, mode?, file?) {
    if (event) {
      file = (event.target as HTMLInputElement).files[0];
    }
    if (file && file.size <= 10000000) {
      this.startEditorLoader(mode);
      this.uploadImage(file, mode);
    }
  }

  startEditorLoader(mode?) {
    mode && mode === 'edit'
      ? (this.isImgUploadEdit = true)
      : (this.isImgUpload = true);
  }

  stopEditorLoader(mode?) {
    mode && mode === 'edit'
      ? (this.isImgUploadEdit = false)
      : (this.isImgUpload = false);
  }

  uploadImage(file?: File, mode?) {
    this.commentsService
      .uploadImage(file, mode, this.subscriptionId)
      .subscribe({
        next: (res) => {
          if (mode && mode === 'edit') {
            this.imgFileInputEdit.nativeElement.value = '';
          } else {
            this.imgFileInput.nativeElement.value = ''; // allow to select the same file
          }
          this.embedImage(res['redirect_url'], mode);
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.imgFileInput.nativeElement.value = ''; // allow to select the same file
          this.imgFileInputEdit.nativeElement.value = '';
          this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
            duration: 3000,
          });
        },
      });
  }

  embedImage(url: string, mode?) {
    if (!this.editorRef && !this.editorRefEdit) {
      return;
    }
    if (mode && mode === 'edit') {
      const range = this.editorRefEdit.getSelection();
      this.editorRefEdit.insertEmbed(range, 'image', url, 'user');
    } else {
      const range = this.editorRef.getSelection();
      this.editorRef.insertEmbed(range, 'image', url, 'user');
    }
    this.stopEditorLoader(mode);
  }

  onContentChange(e, mode, comment?) {
    if (mode === 'edit') {
      this.isCommentChanged = true;
    }
    if (e.html) {
      let result = this.extractContent(e.html);
      if (result === '') {
        if (mode === 'create' && !this.content.includes('<img')) {
          this.content = '';
        }
        if (mode === 'edit' && !comment.edited_text.includes('<img')) {
          comment.edited_text = '';
        }
      }
    }
  }

  extractContent(value) {
    let span = document.createElement('div');
    span.innerHTML = value;
    return span.textContent.trim() || span.innerText.trim();
  }

  onCancelEdit(comment) {
    comment.editable = false;
    this.attachmentID = null;
  }
}

interface User {
  name: string;
  username: string;
}
