@import "../../../styles/variables";

.white-bg {
  // background-color: #fff !important;
  height: calc(100vh - 70px);
}

.comments-wrapper {
  height: calc(100vh - 150px);

  .comment-division {
    .division-1 {
      width: 100%;
      position: sticky;
      overflow-y: scroll;
      border-right: 1px solid #e6e8f0;

      // height: 100vh;
      .active-comment {
        background-color: #f3f6ff;
      }

      mat-card {
        cursor: pointer;
        box-shadow: none;
        border-top: 1px solid #e6e8f0;
        border-bottom: 1px solid #e6e8f0;
        border-radius: 0px;
        background: #ffffff;

        .card-info {
          .batch-caption {
            font-family: $site-font;
            font-style: normal;
            font-weight: normal;
            font-size: 14px;
            line-height: 21px;
            text-decoration-line: underline;
          }

          mat-icon {
            position: absolute;
            right: 20px;
          }
        }

        .profile {
          img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
          }

          .profile-info {
            font-style: normal;
            color: $theme-black;
            padding-left: 10px;

            .username {
              font-weight: 600;
              font-size: 12px;
              line-height: 18px;
              padding-bottom: -10px;
            }

            .comment-time {
              font-weight: 300;
              font-size: 10px;
              line-height: 15px;
            }
          }
        }

        .comments {
          font-style: normal;
          font-weight: normal;
          font-size: 14px;
          line-height: 21px;
          height: fit-content !important;
          width: 100%;

          p {
            width: 100%;
            overflow-wrap: break-word;
          }

          .tagged-users {
            margin-right: 5px;

            .comment-hover {
              .comment-text {
                position: relative;
                font-size: 14px;
                width: 100%;
                margin-top: 5px;

                img {
                  max-width: 200px;
                  max-height: 200px;
                }
              }
            }

          }
        }
      }
    }

    .division-2 {
      position: relative;
      background-color: $theme-white;

      // height: 100vh;
      .comment-section {
        box-shadow: 0 0 4px #00000033;

        .card-info {
          width: 80%;
          padding-left: 20px;
          word-break: break-word;

          .comment-header {
            margin-top: 20px;
            margin-right: 10px;

            // width: 100%;
            p {
              margin-top: 10px;
              font-style: normal;
              font-weight: normal;
              font-size: 14px;
              line-height: 21px;
              text-decoration-line: underline;

            }

            img {
              position: absolute;
              padding-left: 10px;
              margin-top: 5px;
            }
          }
        }

        .action-btn {
          margin-right: 20px;
          display: flex;
          justify-content: flex-end;

          button {
            width: fit-content;
            background: #f3f6ff;
            border-radius: 4px;
            height: 40px;
            margin-left: 10px;
          }

          .resolve-btn {
            background: #52bd94;
            color: $theme-white;
          }
        }
      }

      .tagged-users {
        margin-right: 3px;
      }

      .comments-scroll-wrapper {
        margin: 10px 30px;

        // overflow-y: scroll;
        .no-comments-yet {
          color: $neutral-200;
          height: calc(100vh - 450px);

          .main-msg {
            font: normal normal 400 24px/36px 'Poppins';
          }

          .sub-msg {
            font: normal normal 300 14px/21px "Open Sans", sans-serif;
          }
        }

        .comments-scroll {
          &::-webkit-scrollbar {
            width: 10px;
          }

          /* Track */
          &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px grey;
            border-radius: 5px;
          }

          /* Handle */
          &::-webkit-scrollbar-thumb {
            background: #b8b6b6;
            border-radius: 10px;
            height: 20px;
          }

          /* Handle on hover */
          &::-webkit-scrollbar-thumb:hover {
            background: #b1acac;
          }

          overflow-y: scroll;
          height: calc(100vh - 450px);

          // display: flex;
          // flex-direction: column-reverse;
          .cmt-profile {
            .img {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              object-fit: cover;
            }
          }

          .cmt-profile-picture {
            width: 90%;
            font-style: normal;
            color: $theme-black;
            padding-left: 10px;

            .username {
              font-weight: 600;
              font-size: 12px;
              line-height: 18px;
              padding-bottom: -10px;
            }

            .comment-time {
              // margin-top: -10px;
              font-weight: 300;
              font-size: 10px;
              line-height: 15px;
            }

            .comments {
              margin-bottom: 5px;
              width: 100%;
              max-width: 100%;
              cursor: pointer;
              padding: 6px 10px 6px 6px;
              font-family: normal normal normal 14px/21px "Open Sans", sans-serif !important;
              border-radius: 8px;

              // overflow-wrap: break-word;
              // &:hover {
              .comment-text {
                max-width: 100%;
                width: 100%;
                overflow-wrap: break-word;

                p {
                  word-break: break-word;
                  margin: 0;
                }

                img {
                  max-width: 500px;
                  max-height: 500px;
                }
              }

              // }
              .delete-icon {
                width: 20px;
                height: 20px;
              }
            }

            .hover-color {
              background: #f4f6fa;
            }

            .edit-comment-box {
              .attach-file {
                cursor: pointer;
              }

              // border: 1px solid #e6e8f0;
              // border-radius: 4px;
              // padding-right: 10px;
              // .edit-comment-text {
              //   width: 80%;
              //   padding: 10px;
              //   resize: none;
              // }
              // ul {
              //   width: 13rem;
              //   background-color: #fff;
              //   box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%),
              //     0 3px 1px -2px rgb(0 0 0 / 20%), 0 1px 5px 0 rgb(0 0 0 / 12%);
              //   padding: 0;
              //   li {
              //     padding-left: 1rem;
              //     padding-right: 1rem;
              //     height: 2rem;
              //     min-width: 10rem;
              //     background-color: #fff;
              //     cursor: pointer;
              //     display: -webkit-box;
              //     display: flex;
              //     -webkit-box-align: center;
              //     align-items: center;
              //     -webkit-box-pack: justify;
              //     justify-content: space-between;
              //   }
              // }
              // img {
              //   cursor: pointer;
              //   width: 24px;
              //   width: 24px;
              // }
            }
          }
        }

        .comment-box {
          // margin: 0px 2px;
          // border: 1px solid #e6e8f0;
          box-sizing: border-box;
          border-radius: 4px;
          width: 60%;
          // padding: 20px;
          position: fixed;
          bottom: 10px;

          // padding: 1rem;
          // ul {
          //   width: 13rem;
          //   background-color: #fff;
          //   box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%),
          //     0 3px 1px -2px rgb(0 0 0 / 20%), 0 1px 5px 0 rgb(0 0 0 / 12%);
          //   padding: 0;
          //   li {
          //     padding-left: 1rem;
          //     padding-right: 1rem;
          //     height: 2rem;
          //     min-width: 10rem;
          //     background-color: #fff;
          //     cursor: pointer;
          //     display: -webkit-box;
          //     display: flex;
          //     -webkit-box-align: center;
          //     align-items: center;
          //     -webkit-box-pack: justify;
          //     justify-content: space-between;
          //   }
          // }
          // .cmt-textarea {
          //   width: 100%;
          //   img {
          //     cursor: pointer;
          //     margin-right: 6px;
          //   }
          //   textarea {
          //     resize: none;
          //   }
          // }
          // .add-file {
          //   margin-bottom: -12px;
          //   margin-left: 10px;
          //   cursor: pointer;
          // }
          .attach-file {
            cursor: pointer;
          }

          .box-footer {
            margin-top: 10px;
          }
        }
      }
    }
  }
}

// ::-webkit-scrollbar {
//   width: 0px !important;
// }
.center-placement {
  width: 100%;
  height: 500px !important;
}

.comments-scroll-wrapper,
.division-1,
.division-2 {

  // Scroll bar for comments page
  &::-webkit-scrollbar {
    width: 10px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 5px;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #b8b6b6;
    border-radius: 10px;
    height: 20px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #b1acac;
  }
}

.go-back {
  cursor: pointer;
}

.file-chip-row {
  width: 100%;
  margin-top: 10px;
  .file-chip {
    background-color: rgb(241, 241, 241);
    margin-right: 20px;
    width: max-content;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    position: relative;
    mat-icon {
      font-size: 15px;
      padding-left: 5px;
      cursor: pointer;
      width: 15px;
      height: 15px;
    }
  }
}
