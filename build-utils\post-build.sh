#!/bin/bash
# Copyright © 2018 CrowdANALYTIX, Inc. - All Rights Reserved
#
# Unauthorized copying of this file, via any medium is strictly prohibited.
# CrowdANALYTIX owns the intellectual property in this source code unless
# otherwise stated expressly.
#
# Proprietary and confidential.
#
# Authors: <AUTHORS>
# Project: Shibaura EFPCP UI
echo "Post build script"
cd dist/
cd $(ls -d */|head -n 1)
echo "Switched to `pwd`"
echo "Move en-US to en" # Change actual locale with region 'en-US' to just 'en' for simplicity
mv "en-US" "en"
# echo "Update ---githash--- in footers for development use"
# GITHASH=$(git rev-parse --short=8 HEAD)
# find . -mindepth 2 -maxdepth 2 -type f -name "*.js*" -print0 | xargs -0 sed -i "s/---githash---/$GITHASH/g"
# echo "---githash--- replaced with $GITHASH"
