<div class="wrapper" fxFlex="100" fxLayout="column">
  <!-- loading spinner -->
  <div class="data-loading-spinner" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="90" strokeWidth="3"></mat-spinner>
  </div>

  <!-- stats card -->
  <div *ngIf="!dataLoading" class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px">
    <div class="card-wrapper" fxFlex="100" fxLayout="row" fxLayoutGap="20px">
      <mat-card class="setting-card" *ngFor="let item of stats">
        <div fxLayout="column" class="stats">
          <span class="count">{{item.value}}</span>
          <span class="stat-name">{{item.display_name | underscoreAsSpace | titlecase}}</span>
        </div>
      </mat-card>
    </div>
    <!-- removing the graph as per https://git.crowdanalytix.com/macnica/documents/misumi/-/issues/18 -->
    <!-- API Usage Data -->
    <!-- <div
      fxLayout="row"
      class="chart-container"
      fxLayoutAlign="space-between center"
    >
      <p i18n>API Usage</p> -->
    <!-- filter  -->
    <!-- <mat-form-field appearance="none" class="chart-filter">
        <mat-select i18n-placeholder placeholder="Filter" [(value)]="filter" (selectionChange)="getApiUsageStats($event.value)">
          <mat-option *ngFor="let item of timePeriod" [value]="item.value">
            {{ item.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div> -->
    <!-- chart  -->
    <!-- <mat-card class="chart" *ngIf="lineChartData">
      <canvas
        baseChart
        height="70"
        [data]="lineChartData"
        [options]="lineChartOptions"
        [type]="lineChartType"
      ></canvas>
    </mat-card> -->
  </div>
</div>
