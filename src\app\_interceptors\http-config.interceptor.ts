import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpHeaders,
  HttpXsrfTokenExtractor,
  HttpResponse,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { catchError, map } from 'rxjs/operators';
import { Auth0Service } from '../services/auth0.service';
import { environment } from 'src/environments/environment';
import { tap } from 'rxjs/operators';
@Injectable()
export class HttpConfigInterceptor implements HttpInterceptor {
  constructor(
    private tokenExtractor: HttpXsrfTokenExtractor,
    private router: Router,
    private auth0: Auth0Service
  ) {}
  user;
  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // Set Credentials (<PERSON><PERSON>)
    request.clone({
      withCredentials: true,
    });
    // if (this.tokenExtractor.getToken()) {
    let headers: HttpHeaders;
    if (request.headers.has('Content-Range')) {
      headers = new HttpHeaders({
        'Content-Range': request.headers.get('Content-Range'),
      });
    }
    request = request.clone({
      headers,
    });
    // }

    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        console.log(2);
        if (!navigator.onLine) {
          this.router.navigate(['/offline'], {
            queryParams: { from: this.router.url },
          });
        }
        if (error.status === 403 || error.status === 401) {
          if (localStorage.getItem('user')) {
            this.user = JSON.parse(localStorage.getItem('user'));
          }
          this.auth0.logUserOut();
          localStorage.clear();
        }
        return throwError(error);
      })
    );
  }
}
