import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PinchZoomModule } from 'ngx-pinch-zoom';
import { ImageViewerDialogComponent } from '../_dialogs/image-viewer-dialog/image-viewer-dialog.component';
import { InnerHTMLstylesPipe } from '../_pipe/inner-htmlstyles.pipe';
import { MaterialModule } from '../../material';
import { QuillModule } from 'ngx-quill';
import { TruncatePipe } from '../_pipe/truncate.pipe';
import { CommmaSeperatorPipe } from '../_pipe/commma-seperator.pipe';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { PlainStringPipe } from '../_pipe/plain-string.pipe';
import { CommentPipe } from '../_pipe/comment.pipe';
import { UnderscoreAsSpacePipe } from '../_pipe/underscore-as-space.pipe';

@NgModule({
  declarations: [
    InnerHTMLstylesPipe,
    TruncatePipe,
    CommmaSeperatorPipe,
    ImageViewerDialogComponent,
    PlainStringPipe,
    CommentPipe,
    UnderscoreAsSpacePipe,
  ],
  imports: [
    CommonModule,
    PinchZoomModule,
    MatInputModule,
    FormsModule,
    MaterialModule,
    ReactiveFormsModule,
    QuillModule,
    InfiniteScrollModule,
  ],
  exports: [
    InnerHTMLstylesPipe,
    TruncatePipe,
    CommmaSeperatorPipe,
    ImageViewerDialogComponent,
    PlainStringPipe,
    CommentPipe,
    CommonModule,
    UnderscoreAsSpacePipe,
    PinchZoomModule,
    MatInputModule,
    FormsModule,
    MaterialModule,
    ReactiveFormsModule,
    QuillModule,
    InfiniteScrollModule,
  ],
})
export class SharedModule {}
