import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Globals } from '../_globals/endpoints.global';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Auth0Service } from './auth0.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from 'src/environments/environment';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  permissionObject;
  appClass;
  appIdentifier;
  user;
  private httpOptions: HttpHeaders;
  appPermissionsAvailable = new BehaviorSubject<boolean>(false);
  isAppPermissionsAvailable = this.appPermissionsAvailable.asObservable();
  // private endpoints: any = ENDPOINTS;
  constructor(
    private globals: Globals,
    private http: HttpClient,
    private auth0: Auth0Service,
    private snackBar: MatSnackBar
  ) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * user me endpoint
   * @param subs_id
   * @returns
   */
  me = (subs_id): Observable<any> => {
    const meEndPoint = this.globals.urlJoinWithParam('user', 'me', subs_id);
    return this.http.get(meEndPoint).pipe(
      map((response) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * API Usage Settings
   * @param subs_id
   * @param filter
   * @returns
   */
  apiUsage = (subs_id, filter): Observable<any> => {
    const apiUsageEndPoint = this.globals.urlJoinWithParam(
      'settings',
      'apiUsage',
      subs_id
    );
    const options = {
      params: new HttpParams().set('filter', filter),
    };
    return this.http.get(apiUsageEndPoint, options).pipe(
      map((response) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get app permissions
   * @param subs_id
   * @returns
   */
  getAppPermissions = (subs_id) => {
    const getPermissionEndpoint = this.globals.urlJoinWithParam(
      'user',
      'permissions',
      subs_id
    );
    return this.http
      .get(getPermissionEndpoint, {
        headers: this.httpOptions,
      })
      .pipe(
        map((response: any) => {
          this.permissionObject = response.result;
          this.checkAppPermissions(true);
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  get appPermissions(): any {
    return this.permissionObject;
  }

  set appPermissions(val) {
    this.permissionObject = val;
  }

  /**
   * set app theme
   * @param subs_id
   */

  setTheme = (subs_id) => {
    this.me(subs_id).subscribe({
      next: (resp) => {
        localStorage.setItem('user', JSON.stringify(resp.result));
        let app = resp.result.theme_class;
        if (app == 'default') {
          this.appClass = '';
          document.body.className = '';
        } else {
          this.appClass = app + '-app-theme';
          document.body.classList.add(this.appClass);
        }
      },
      error: (error) => {
        this.user = JSON.parse(localStorage.getItem('user'));
        this.auth0.logUserOut();
        this.snackBar.open(error || 'User session invalid', 'OK', {
          duration: 3000,
          panelClass: ['error-snackbar'],
        });
      },
    });
  };

  /**
   * sets the appPermissionsAvailable to true once permissions api has responded
   * @param val
   */
  checkAppPermissions = (val: boolean) => {
    this.appPermissionsAvailable.next(val);
  };

  /**
   * get user product details view mode preference
   * @param value
   * @param days
   */
  getUserPreferenceFromCookie = () => {
    var nameEQ = 'view_mode' + '=';
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
      var c = ca[i];
      while (c.charAt(0) == ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  };
}
