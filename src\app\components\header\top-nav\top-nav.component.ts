import { Component, OnInit } from '@angular/core';
import { AuthService, User } from '@auth0/auth0-angular';
import { Auth0Service } from '../../../services/auth0.service';
import { UserService } from 'src/app/services/user.service';
import { ActivatedRoute, Params } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from '../../../../environments/environment';
import { HttpErrorResponse } from '@angular/common/http';
@Component({
  selector: 'app-top-nav',
  templateUrl: './top-nav.component.html',
  styleUrls: ['./top-nav.component.scss'],
})
export class TopNavComponent implements OnInit {
  userDisplayPicture =
    'https://s3-alpha-sig.figma.com/img/30b1/612c/a844d851f33dbc86e2e1b1a558d05f1f?Expires=1640563200&Signature=P~7si87mBOGK5iFnSQTKxOtn-6FEWgPLOjE02n26S6ZC5CzcgwC~Fi8pgLpMwU22IdMF50Knq75Jvhg5CPtcGVO-PPzs6gLnEvxcAS9pxrnbXwTzxa6YQl5wyfAPFkbMObqZsnOzSfwSv2FQJPUJhTcFcWr8V96NGHGVYuZGXsRSrsnIBrQr4MDQGHd~yGmQMtQMgrNGn4Z4dlkiAAhmvvKb0t9SyoltxWgly5f9SJ2PjIGNtP8YQ4qn6Xxu2ozTCDDIJ8LCKRyeycb3IaxGTj18Wojm9rODBfKK3IkXQElWLXkgFWxVaWJYDSOcRvYxi5UeB5C1THe5ou9Lfe70BQ__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA';
  user;
  subscriptionId;
  app;
  appClass;

  constructor(
    public auth: AuthService,
    public appAuthService: Auth0Service,
    public userService: UserService,
    private activatedRoute: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    // if (localStorage.getItem('user')) {
    //   this.user = JSON.parse(localStorage.getItem('user'));
    // } else {
    //   if (this.subscriptionId) {
    //     this.userService.me(this.subscriptionId).subscribe({
    //       next: (resp) => {
    //         this.user = resp.result;
    //         localStorage.setItem('user', JSON.stringify(resp.result));
    //       },
    //     });
    //   }
    // }

    if (this.subscriptionId) {
      this.userService.me(this.subscriptionId).subscribe({
        next: (resp) => {
          this.user = resp.result;
          localStorage.setItem('user', JSON.stringify(resp.result));
          let app = resp.result.theme_class;
          // let app = 'ad';
          // if (app == 'default') {
          //   this.appClass = '';
          //   document.body.className = '';
          // } else {
          //   this.appClass = app + '-app-theme';
          //   document.body.classList.add(this.appClass);
          // }
        },
        error: (error: HttpErrorResponse) => {
          this.snackBar.open('User session invalid', 'OK', {
            duration: 3000,
            panelClass: ['error-snackbar'],
          });
        },
      });
    }
  }

  stopPropagation(event) {
    event.stopPropagation();
  }

  logUserOut = () => {
    localStorage.clear();
    this.appAuthService.logUserOut();
  };
}
