import { Component, OnInit } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { Title } from '@angular/platform-browser';
import {
  ActivatedRoute,
  Router,
  NavigationEnd,
  Event as NavigationEvent,
  NavigationStart,
} from '@angular/router';
import { filter, map, mergeMap } from 'rxjs/operators';
import { UserService } from 'src/app/services/user.service';
import { AuthService } from '@auth0/auth0-angular';
import { Auth0Service } from './services/auth0.service';
import { environment } from 'src/environments/environment';
import { HomeService } from './services/home.service';

// import { AuthService } from '@auth0/auth0-angular';
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent {
  title = 'datax-v2';
  isscroll=false;
  isLowerResolution: boolean | undefined;
  event$;
  currentUrl: any;
  subscriptionId;
  moduleName;
  permissionsAvailable;
  constructor(
    private homeService:HomeService,
    public router: Router,
    private iconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
    private activatedRoute: ActivatedRoute,
    private titleService: Title, // public auth: AuthService
    private userService: UserService,
    public auth: AuthService,
    public appAuth: Auth0Service
  ) {}

  ngOnInit() {
    this.homeService.messageSource.subscribe((data)=>{
      if(data){
        this.isscroll = true;

      }
      else{
        this.isscroll = false;
      }
    })
    this.userService.isAppPermissionsAvailable.subscribe((val) => {
      this.permissionsAvailable = val;
    });
    // get subscription id from location url
    const params = new URL(location.href).searchParams;
    // save to local storage if valid param subscription id
    // this happens only one when app loads first time
    // so just grab and keep it
    if (params.get('sub')) {
      localStorage.setItem('SubscriptionID', params.get('sub'));
    }
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    if (this.subscriptionId && this.subscriptionId != 'undefined') {
      // set app theme
      this.userService.setTheme(this.subscriptionId);
    }

    // check for window width and height to show 'resolution unsupported' msg
    if (window.innerWidth < 1280) {
      this.isLowerResolution = true;
    } else {
      this.isLowerResolution = false;
    }
    // child route title
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.moduleName = window.location.hostname.split('.')[0];
        const rt = this.getChild(this.activatedRoute);
        rt.data.subscribe((data) => {
          if (this.moduleName == 'r2e') {
            this.titleService.setTitle(
              $localize`dataX R2E: Item Data Enrichment - ` + data.title
            );
          } else if (this.moduleName == 'r3b') {
            this.titleService.setTitle(
              $localize`dataX R3B: Item Data Audit & Enhancement - ` +
                data.title
            );
          }
        });
      });

    this.iconRegistry.addSvgIcon(
      'home',
      this.sanitizer.bypassSecurityTrustResourceUrl(
        'assets/images/side-nav-svg/home.svg'
      )
    );
    this.iconRegistry.addSvgIcon(
      'chevronLeft',
      this.sanitizer.bypassSecurityTrustResourceUrl(
        'assets/images/image-viewer/chevron_left.svg'
      )
    );

    this.iconRegistry.addSvgIcon(
      'chevronRight',
      this.sanitizer.bypassSecurityTrustResourceUrl(
        'assets/images/image-viewer/chevron_right.svg'
      )
    );

    // auth0 error handling
    this.auth.error$.subscribe((error) => {
      console.log(error);
      this.appAuth.logUserOut();
    });
  }

  getChild(activatedRoute: ActivatedRoute) {
    if (activatedRoute.firstChild) {
      return this.getChild(activatedRoute.firstChild);
    } else {
      return activatedRoute;
    }
  }

  ngOnDestroy() {
    this.event$.unsubscribe();
  }

  /**
   * Low resolution info
   * @param event
   */
  onResize = (event: any) => {
    if (event.target.innerWidth < 1280) {
      this.isLowerResolution = true;
    } else {
      this.isLowerResolution = false;
    }
  };
}
