import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProductDetailsComponent } from './product-details/product-details.component';
import { ReviewModeComponent } from './review-mode/review-mode.component';
import { AuthGuard } from '@auth0/auth0-angular';
import { AppAuthGuard } from '../_guards/auth.guard';
import { CommentsComponent } from './comments/comments.component';

const routes: Routes = [
  {
    path: 'product-details',
    component: ProductDetailsComponent,
    data: { title: $localize`Product Details` },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'review-mode',
    component: ReviewModeComponent,
    data: { title: $localize`Review Mode` },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'comments',
    component: CommentsComponent,
    data: { title: $localize`Comments` },
    canActivate: [AuthGuard, AppAuthGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CustomersRoutingModule {}
