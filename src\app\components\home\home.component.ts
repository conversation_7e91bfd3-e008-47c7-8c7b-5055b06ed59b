import {
  AfterViewInit,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormArray,
  Validators,
} from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { SidePanelService } from '../../services/side-panel.service';
import { Auth0Service } from '../../services/auth0.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { UserService } from 'src/app/services/user.service';
// import { AuthService } from '@auth0/auth0-angular';
import { HttpErrorResponse } from '@angular/common/http';
import moment from 'moment';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HomeService } from '../../services/home.service';
import { Subscription, Observable, timer, Subject, BehaviorSubject, interval } from 'rxjs';
import {map, startWith} from 'rxjs/operators';
import { FileUploadService } from '../../services/file-upload.service';
import { SnackbarService } from '../../services/snackbar.service';
import { ConfirmDialog } from '../../_dialogs/confirm-dialog/confirm-dialog.component';
import { MatDialogRef, MatDialog } from '@angular/material/dialog';
import { ApproveBatchDialog } from 'src/app/_dialogs/approve-batch/approve-batch.component';
import { ErrorStateMatcher } from '@angular/material/core';
import ColorPicker from 'simple-color-picker';
import { switchMap, takeUntil, concatMap } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';

export interface BatchTableList {
  batch_id: string;
  name: string;
  labels: any;
  eta: string;
  total_Rows: number;
  accepted: any;
  others: any;
  actions: number;
}

/** Error when invalid control is dirty, touched */
export class MyErrorStateMatcher implements ErrorStateMatcher {
  isErrorState(control: FormControl | null): boolean {
    return control && control.invalid && (control.dirty || control.touched);
  }
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent implements OnInit {
  uploadBatch: boolean = true;
  batchLog: boolean = false;
  addTag: boolean = false;
  files: File[] = [];
  fileToUpload: File;
  isUpload: boolean = true;
  progress: boolean = false;
  cancelBatch: boolean = false;
  myDatePicker;
  productHeaderData = [
    { display: $localize`:header:Batch ID`, slug: 'Batch ID' },
    {
      display: $localize`:header:Name & Description`,
      slug: 'Name & Description',
    },
    { display: $localize`:header:Tags`, slug: 'Tags' },
    { display: $localize`:header:Created On`, slug: 'Created On' },
    { display: $localize`:header:ETA`, slug: 'ETA' },
    { display: $localize`:header:References`, slug: 'References' },
    { display: $localize`:header:SC Batch ID`, slug: 'SC Batch ID' },
    { display: $localize`:header:Accepted`, slug: 'Accepted' },
    { display: $localize`:header:Actions`, slug: 'Actions' },
    { display: $localize`:header:Comments`, slug: 'Comments' },
    // { display: $localize`:header:More`, slug: 'More' },
  ];
  displayedColumns;
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: Date;
  customStartDate: Date;
  maxDate: Date;
  minDate: Date;
  today;
  pageNumber;
  selected = 'recent';
  selectedStatus = '';
  start_date;
  end_date;
  label: string[] = [];
  page: number;
  size: number;
  totalItems: number;
  totalPages: number;
  search: any;
  status: any;
  activeTabName: any;
  SubscriptionID: any;
  //timer
  getBatchListTimer: Subscription;
  batchList: any;
  homeDataSource;
  labelList: any;
  currentBatch: any;
  selectedIndex;
  tableDataLoading: boolean = false;
  dataLoading: boolean = false;
  createlabelFormValues: any;
  uploadBatchFormValues: any;
  uploadProgress: number;
  fileUploadStatus: any = 'Idle';
  uploadButtonLabel: String = $localize`Upload`;
  dropzoneLabel: String = $localize`Drag and Drop your files here`;
  activeChip;
  labelColor: any;
  headercount: any;
  logList;
  logsLoading: boolean = false;
  permissionsObject;
  datetime;
  downloadOutputFileText = $localize`Download output file`;
  downloadInsufficientFileSKUText = $localize`Download insufficient data SKUs`;
  downloadDuplicateFileSKUText = $localize`Download duplicate SKUs`;
  downloadOtherFileSKUText = $localize`Download other SKUs`;
  uploadInterrupted: boolean = false;
  refFlag = false;

  // color picker start
  // 
  opacityLevels = [
    { value: 0.1, viewValue: '10%' },
    { value: 0.2, viewValue: '20%' },
    { value: 0.3, viewValue: '30%' },
    { value: 0.4, viewValue: '40%' },
    { value: 0.5, viewValue: '50%' },
    { value: 0.6, viewValue: '60%' },
    { value: 0.7, viewValue: '70%' },
    { value: 0.8, viewValue: '80%' },
    { value: 0.9, viewValue: '90%' },
    { value: 1.0, viewValue: '100%' },

  ];
  selectedOpacity: number = 0.1; // Default value set to 50%

  // bg Color Picker
  colorPicker = new ColorPicker(
    {
      color: '#000000',
      background: '#454545',
      width: 128,
      height: 100,
    }
  );
  textColorPicker = new ColorPicker(
    {
      color: '#FFFFFF',
      background: '#454545',
      width: 124,
      height: 100,
    }
  );
  color:string = '#000000';

  // color picker end
  // 
  dialogRef: MatDialogRef<ConfirmDialog, any>;
  approveBatchDialogRef: MatDialogRef<ApproveBatchDialog, any>;

  tabList: any[] = [
    {
      display: $localize`:bucket:In Queue`,
      value: 'in_queue',
      desc: $localize`:bucket description:Batches that are undergoing validation prior to processing`,
    },
    {
      display: $localize`:bucket:In Progress`,
      value: 'in_progress',
      desc: $localize`:bucket description:Batches that are undergoing attribute enrichment`,
    },
    {
      display: $localize`:bucket:Processed`,
      value: 'processed',
      desc: $localize`:bucket description:Batches that have completed the enrichment process. Possible actions: Approve the batch / Download enriched data / Download input data`,
    },
    {
      display: $localize`:bucket:Approved`,
      value: 'approved',
      desc: $localize`:bucket description:Batches that have been approved post-enrichment. Possible actions: Download enriched data / Download input file`,
    },
    {
      display: $localize`:bucket:Cancelled`,
      value: 'cancelled',
      desc: $localize`:bucket description:Batches that could not be processed because of invalid data`,
    },
  ];
  tabIndexWithData;
  inputTemplates: any[] = [];
  outputTemplates: any[] = [];
  modifyEtaMsg: string = $localize`Modify ETA`;
  addEtaMsg: string = $localize`Add ETA`;
  matcher = new MyErrorStateMatcher();
  tagCheckRegex = '^(?!.*<[^>]+>).*';
  fetchTableData: Subscription;
  // 
  textColor: any;
  opacity: any = 1;
  textOpacity: any = 1;
  testBgOpacityChange: any;
  tagBgColorDecoration: { rgba: string; hexWithOpacity: string; };
  tagTextColorDecoration: { rgba: string; hexWithOpacity: string; };
  textColorWithOpacity: string;
  tagBackgroundColor: any;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild('colorPickerHolder') colorPickerHolder!: ElementRef;
  // search by tag name variables
  filteredTagOptions: Observable<any[]>
  selectedTagFormControl = new FormControl();
  tagQueryCtrl = new FormControl('');
  selectedTagValues = [];
  selectedTagObjectList = [];
  @ViewChild('searchTextBoxRef') searchTextBox: ElementRef;

  stopPolling$ = new Subject<boolean>();
  exportDownloadUrl: string;

  // 

  constructor(
    private sidepanel: SidePanelService,
    private auth0: Auth0Service,
    private activatedRoute: ActivatedRoute,
    private userService: UserService, // public auth: AuthService
    public matSnackbar: MatSnackBar,
    private homeService: HomeService,
    private fb: FormBuilder,
    public fileUploadService: FileUploadService,
    private router: Router,
    private activatedroute: ActivatedRoute,
    private snackbarService: SnackbarService,
    private dialog: MatDialog,
    private http: HttpClient
  ) {}


  public createLabelForm: FormGroup;
  public uploadBatchForm: FormGroup;

  ngOnDestroy() {
    this.fetchTableData.unsubscribe();
  }

  ngOnInit() {
    this.displayedColumns = this.productHeaderData.map((column) => column.slug);
    this.createLabelForm = this.fb.group({
      name: ['', [Validators.required, Validators.pattern(this.tagCheckRegex), this.noWhitespaceValidator]],
      title: ['', [Validators.required, Validators.pattern(this.tagCheckRegex), this.noWhitespaceValidator]],
    });
    
    this.uploadBatchForm = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          this.noWhitespaceValidator,
          Validators.pattern(this.tagCheckRegex),
          Validators.maxLength(100),
        ],
      ],
      sc_batch_id: ['', [this.noWhitespaceValidator,
        Validators.pattern(this.tagCheckRegex),
        Validators.maxLength(15)]],
      output_template_id: ['', Validators.required],
      input_template_id: ['', Validators.required],
      description: ['', [Validators.maxLength(2000), this.noWhitespaceValidator, Validators.pattern(this.tagCheckRegex)]],
      // reference: ['', [Validators.maxLength(1000)]],
      reference: this.fb.array([
        this.fb.control(null, [Validators.maxLength(1000), this.noWhitespaceValidator, Validators.pattern(this.tagCheckRegex)]),
      ]),
    });

    this.dataLoading = true;
    //set table data
    this.today = new Date();
    this.search = '';
    this.page = 1;
    this.size = 50;
    this.activeTabName = 'IN_QUEUE';
    this.start_date = '';
    this.end_date = '';
    this.currentDate = moment().format('YYYY-MM-DD');
    const currentyear = new Date().getFullYear();
    this.minDate = new Date(currentyear - 20, 0, 1);
    this.maxDate = new Date();
    this.selected = 'recent';
    this.selectedStatus = '';

    // retrieve subscription id
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.SubscriptionID = params.sub;
    });
    // retrieve home page permissionsObject
    this.permissionsObject = this.userService.appPermissions.permissions;
    this.selected = 'recent';
    const currentYear = new Date().getFullYear();
    this.minDate = new Date(currentYear - 20, 0, 1);
    this.maxDate = new Date();
    this.currentDate = moment().format('YYYY-MM-DD');
    this.datePickerOptions = [
      {
        display: 'Recent',
        value: 'recent',
        start_date: '',
      },
      {
        display: $localize`Last Week`,
        value: 'last_week',
        start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
      },
      {
        display: $localize`Last Month`,
        value: 'last_month',
        start_date: moment()
          .subtract(1, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: $localize`Last Quarter`,
        value: 'last_quarter',
        start_date: moment()
          .subtract(3, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        value: 'custom_range',
      },
    ];
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date,
      this.selectedTagValues
    );
    // get list of tags
    this.getLabelList(this.SubscriptionID);
    // get tag autocomplete filtered options
    this.filteredTagOptions = this.tagQueryCtrl.valueChanges
      .pipe(
        startWith<string>(''),
        map(name => this._filter(name))
      );
  }

  /****************************** tag multiselect search *********************************/
  /**
   * Used to filter tags based on search input 
   */
  private _filter = (name: string): String[] => {
    const filterValue = name.toLowerCase();
    // Set previously selected values to retain the selected checkbox state 
    this.selectedTagFormControl.patchValue(this.selectedTagObjectList);
    if(this.labelList.length) {
      let filteredList = this.labelList.filter(option => option.name.toLowerCase().indexOf(filterValue) === 0);
      return filteredList;
    } else {
     return []
    }
  }


  /*** 
   * get datat for selected tag
   */
   getDataForSelectedTag = () => {
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date, 
      this.selectedTagValues
    );
    
  }

  /**
   * on tag selection change
   */
  tagSelectionChange = (event) => {
    if (event.isUserInput) {
      let index = this.selectedTagValues.indexOf(event.source.value.slug);
      if(index > -1) {
        this.selectedTagValues.splice(index, 1)
        this.selectedTagObjectList.splice(index, 1)
      }
      else {
        this.selectedTagValues.push(event.source.value.slug)
        this.selectedTagObjectList.push(event.source.value)
      }
    }
  }

  openedChange = (e) => {
    // Set search textbox value as empty while opening selectbox 
    this.tagQueryCtrl.patchValue('');
    // Focus to search textbox while clicking on selectbox
    if (e == true) {
      this.searchTextBox.nativeElement.focus();
    } else {
      // Set selected values to retain the selected checkbox state 
      this.getDataForSelectedTag();
    }
  }

  /**
   * Clearing search textbox value 
   */
  clearSearch = (event) => {
    event.stopPropagation();
    this.tagQueryCtrl.patchValue('');
  }

/*************************** tag multiselect search ends *************************************/

  // color picker start
  // 
  updateColorDisplay(opacityValue: any) {
    // console.log($event);
    this.opacity = opacityValue;
    // console.log('opacity: ', this.opacity);
    // ****
     this.tagBgColorDecoration = this.hexToRgba(this.color, this.opacity);
    // console.log(this.tagBgColorDecoration.rgba);
    // console.log(this.tagBgColorDecoration.hexWithOpacity);
    this.tagBackgroundColor = this.tagBgColorDecoration.hexWithOpacity;
    // ****
    // this.testBgOpacityChange = this.opacity;
    }

    updateTextColorOpacity(textOpacity: any) {
      this.textOpacity = textOpacity;
      // console.log('textOpacity: ', this.textOpacity);
      // ****
     this.tagTextColorDecoration = this.hexToRgba(this.textColor, this.textOpacity);
    //  console.log(this.tagTextColorDecoration.rgba);
    //  console.log(this.tagTextColorDecoration.hexWithOpacity);
     this.textColorWithOpacity = this.tagTextColorDecoration.hexWithOpacity;
    //  console.log('textColorWithOpacity: ', this.textColorWithOpacity);
     
     // ****
      }
  // 
  onTagBgColorOpacityChange(event: any): void {
    this.selectedOpacity = event.target.value;
    // console.log('Selected tagBgColor opacity: ', this.selectedOpacity);
  }
  //

  // color picker end
  /**
  Custom Validator to restrict whitespace in Input fields
  **/
  public noWhitespaceValidator(control: FormControl) {
    if(control.value) {
      const isWhitespace = (control.value || '').trim().length === 0;
      const isValid = !isWhitespace;
      return isValid ? null : { whitespace: true };
    }
  }

  /**
   * return table with given tags
   * @param tag 
   */
  onTagSelectAfterQuery = (tag) => {
    console.log(tag)
  }

  /**
   * get Date
   * @param range
   * @returns
   */
  getDataForDate = (range: string) => {
    // do nothing for custom range
    if (this.selected == 'custom_range') {
      return null;
    }
    (this.customStartDate = null), (this.customEndDate = null);
    // for others
    let interval = this.datePickerOptions.filter((item) => {
      return item.value === range;
    })[0];
    this.start_date = interval['start_date'];
    if (this.selected == 'recent') {
      this.end_date = '';
    } else {
      this.end_date = this.currentDate;
    }
    this.page = 1;
    this.homeDataSource = [];
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date,
      this.selectedTagValues
    );
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID,
      this.selectedTagValues
    );
  };
  sd;
  ed;
  /**
   * set date range
   * @param dateRangeStart
   * @param dateRangeEnd
   */
  dateRangeChange = (dateRangeStart, dateRangeEnd) => {
    if (moment(dateRangeStart).isValid() && moment(dateRangeEnd).isValid()) {
      this.selected = 'custom_range';
      this.sd = moment(dateRangeStart).format('YYYY-MM-DD');
      this.ed = moment(dateRangeEnd).format('YYYY-MM-DD');
      this.page = 1;
      if (this.selected == 'custom_range') {
        this.start_date = this.sd;
        this.end_date = this.ed;
      }
      this.homeDataSource = [];
      this.getCount(
        this.SubscriptionID,
        this.search,
        this.start_date,
        this.end_date,
        this.selectedTagValues
      );
      this.getTable(
        this.page,
        this.size,
        this.activeTabName,
        this.search,
        this.start_date,
        this.end_date,
        this.SubscriptionID,
        this.selectedTagValues
      );
    }
  };

  /**
   * Search based on keyword
   * @param value
   */

  getSearchValue = (value) => {
    //
    this.search = value.trim();
    this.page = 1;
    this.homeDataSource = [];
    this.activeTabName = this.activeTabName;
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date,
      this.selectedTagValues
    );
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID,
      this.selectedTagValues
    );
    // this.paginator.firstPage();
  };

  resetSearch = () => {
    this.search = '';
    this.homeDataSource = [];
    this.activeTabName = this.activeTabName;
    // console.log(this.activeTabName)
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date,
      this.selectedTagValues
    );
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID,
      this.selectedTagValues
    );
  };

  reset = () => {
    this.search = '';
    this.page = 1;
    this.size = 50;
    this.selectedIndex = 0;
    this.start_date = '';
    this.end_date = '';
    this.customStartDate = null;
    this.customEndDate = null;
    this.activeTabName = 'IN_QUEUE';
    this.selected = 'recent';
    this.selectedStatus = '';
    this.homeDataSource = [];
    this.selectedIndex = 0;
    this.selectedTagValues = [];
    this.tagQueryCtrl.patchValue('');
    this.selectedTagObjectList = [];
    this.selectedTagFormControl.patchValue([]);
    console.log(this.selectedTagFormControl.value)
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date,
      this.selectedTagValues
    );
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID,
      this.selectedTagValues
    );
  };

  /**
   * toggle side panel
   * @param value
   * @param batch_id
   */
  toggleSideNav = (value, batch_id) => {
    // ==================================================== color picker start
        setTimeout(() => {
          this.colorPicker.appendTo('#colorPicker');
          this.textColorPicker.appendTo('#textColorPicker');
        }, 100);
        // console.log(this.uploadBatchForm);
        this.colorPicker.onChange((color: any) => {
          // console.log(this.colorPicker.getColor());
          this.color = color;
          // this.tagBackgroundColor = color;
          // console.log('tag bg hexa: ', this.color);
          
          // ****
          // this.tagBgColorDecoration = this.hexToRgba(this.color, this.opacity);
          // console.log(this.tagBgColorDecoration);
          // console.log(this.tagBgColorDecoration.rgba);
          // console.log(this.tagBgColorDecoration.hexWithOpacity);
          // this.tagBackgroundColor = this.tagBgColorDecoration.hexWithOpacity;
          // ****
        });
        // 
        this.textColorPicker.onChange((color: any) => {
          // console.log(this.colorPicker.getColor());
          this.textColor = color;
          // this.tagBackgroundColor = color;
          // console.log('tag text hexa: ', this.textColor);

          // ****
        // this.tagTextColorDecoration = this.hexToRgba(this.textColor, this.textOpacity);
        // console.log(this.tagTextColorDecoration.rgba);
        // console.log(this.tagTextColorDecoration.hexWithOpacity);
        // this.textColor = this.tagTextColorDecoration.hexWithOpacity;
        // ****
        });
        // 

    // ==================================================== color picker end

    this.currentBatch = batch_id;
    if (value == 'uploadBatch') {
      this.batchLog = false;
      this.addTag = false;
      this.uploadBatch = true;
      this.uploadInterrupted = false;
      this.getInputOutputTemplates();
      this.homeService.messageSource.next(true);
    } else if (value == 'batchLog') {
      this.addTag = false;
      this.uploadBatch = false;
      this.batchLog = true;
    } else {
      this.uploadBatch = false;
      this.batchLog = false;
      this.addTag = true;
    }
    this.sidepanel.setShowNav(true);
  };

  /**
   * get template id
   * @param val file type selected
   */
  getInputOutputTemplates = () => {
    this.homeService.getTemplateId(this.SubscriptionID, 'INPUT').subscribe({
      next: (resp) => {
        this.inputTemplates = resp.result;
        // set default input value on getting data from server
        this.uploadBatchForm.patchValue({
          input_template_id: this.inputTemplates[0].template_id,
        });
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
    this.homeService.getTemplateId(this.SubscriptionID, 'OUTPUT').subscribe({
      next: (resp) => {
        this.outputTemplates = resp.result;
        // set default output value on getting data from server
        this.uploadBatchForm.patchValue({
          output_template_id: this.outputTemplates[0].template_id,
        });
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
  };

    // *********************
    hexToRgba(hex: string, opacity: number): { rgba: string, hexWithOpacity: string } {
      // console.log('Initial hex: ', hex);
      // console.log('Initial opacity: ', opacity);
  
      
      // Ensure the hex code is in the correct format
      if (hex.charAt(0) === '#') {
        hex = hex.substr(1);
      // console.log('second hex: ', hex);
  
      }
    
      // Convert shorthand hex code to full form if necessary
      if (hex.length === 3) {
        hex = hex.split('').map(char => char + char).join('');
        // console.log('third hex: ', hex);
  
        
      }
    
      // Parse the r, g, b values
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
    
      // Create RGBA string
      const rgba = `rgba(${r}, ${g}, ${b}, ${opacity})`;
    
      // Convert opacity to hex
      const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0').toUpperCase();
      // console.log('opacity to hex: ', alpha);
      
    
      // Create hex with opacity string
      const hexWithOpacity = `#${hex}${alpha}`;
      // console.log('rgba: ', rgba);
      // console.log('hexWithOpacity: ', hexWithOpacity);
  
      
    
      return { rgba, hexWithOpacity };
    }
    
    // Example usage
    // const color = '#000000';
    // const opacity = 0.33;
    
    
    
    
    // *********************
  

  /**
   * close panel
   */
  closeSidepanel = () => {
    // 
    this.opacity = 1;
    this.textOpacity = 1;
    // console.log('tagBackgroundColor: ',this.tagBackgroundColor);
    // console.log('opacity: ',this.opacity);
    // console.log('testBgOpacityChange: ',this.testBgOpacityChange);
    this.colorPicker.setColor('#000000');
    this.textColorPicker.setColor('#FFFFFF');
    //

    this.createLabelForm.reset();
    this.homeService.messageSource.next(false);
    this.resetTagSelected();
    this.sidepanel.toggleNavState();
    // this.getLabelList(this.Su  bscriptionID);
    this.files = [];
    this.uploadBatchForm.reset();
    this.refFlag = false;

    const control = <FormArray>this.uploadBatchForm.controls['reference'];
    // console.log(control.length);
    for (let i = control.length - 1; i >= 1; i--) {
      control.removeAt(i);
    }

    // console.log(this.files);
  };

  /**
   * on successful copy to clipboard
   */
  showSnackbar = () => {
    this.matSnackbar.open($localize`Copied!`, 'OK', {
      duration: 3000,
      horizontalPosition: 'left',
      verticalPosition: 'bottom',
    });
  };

  /**
   *
   * @param event file selection
   */
  onSelect = (event) => {
    if (this.files.length < 1) {
      // console.log(event)
      if (event?.addedFiles[0]?.size == 0) {
        // console.log("Empty file")
        this.showMessage($localize`File can't be empty`);
      } else {
        this.fileToUpload = event?.addedFiles ? event.addedFiles[0] : undefined;
        event?.addedFiles && this.files.push(...event?.addedFiles);
      }
    } else {
      this.onRemove(event);
    }
    this.isUpload = false;
  };

  /**
   * upload Batch file
   */
  upload = () => {
    this.dropzoneLabel = 'Uploading..';
    this.uploadButtonLabel = $localize`Uploading..`;
    this.fileUploadStatus = 'Uploading';
    // disable form when uploading file
    this.uploadBatchForm.disable();
    // retry upload
    this.uploadInterrupted == true ? this.retryUpload() : this.freshUpload();
  };

  /**
   * fresh batch file upload
   */
  freshUpload = () => {
    this.uploadProgress = 0;
    this.uploadBatchFormValues = this.uploadBatchForm.value;
    const filteredRef = this.uploadBatchFormValues.reference.filter(
      (ref) => ref != null && ref != ''
    );
    this.fileUploadService
      .uploadData(
        this.fileToUpload,
        this.SubscriptionID,
        this.uploadBatchFormValues.name,
        this.uploadBatchFormValues.sc_batch_id,
        this.uploadBatchFormValues.input_template_id,
        this.uploadBatchFormValues.output_template_id,
        this.uploadBatchFormValues.description,
        filteredRef
      )
      .subscribe({
        next: (progress) => {
          this.uploadProgress = progress;
          if (this.uploadProgress === 100) {
            // adding timeout of 1s to show progress bar for very small files that uploads instantaneously
            setTimeout(() => {
              this.fileToUpload = undefined;
              this.files = [];
              this.fileUploadStatus = 'Uploaded';
              this.dropzoneLabel = $localize`Drag and Drop your files here`;
              this.uploadButtonLabel = $localize`Upload`;
              this.showMessage($localize`Upload Complete`);
              // enable form after file uploaded successfully
              this.uploadBatchForm.enable();
              this.uploadBatchForm.reset();
              this.isUpload = true;
              // upload complete API
              this.fileUploadService.uploadCompelte().subscribe({
                next: (resp) => {},
                complete: () => {
                  // this.sidepanel.toggleNavState();
                  this.closeSidepanel();
                  // reset form array `references`
                  const control = <FormArray>(
                    this.uploadBatchForm.controls['reference']
                  );
                  for (let i = control.length - 1; i >= 1; i--) {
                    control.removeAt(i);
                  }
                  this.getCount(
                    this.SubscriptionID,
                    this.search,
                    this.start_date,
                    this.end_date,
                    this.selectedTagValues
                  );
                  this.getTable(
                    this.page,
                    this.size,
                    this.activeTabName,
                    this.search,
                    this.start_date,
                    this.end_date,
                    this.SubscriptionID,
                    this.selectedTagValues
                  );
                },
              });
            }, 1000);
          }
        },
        error: (error) => {
          // enable form after file uploaded successfully
          this.uploadBatchForm.enable();
          this.uploadBatchForm.reset();
          // this.sidepanel.toggleNavState();
          // prepopulate formats
          this.uploadBatchForm.patchValue({
            input_template_id: this.inputTemplates[0].template_id,
            output_template_id: this.outputTemplates[0].template_id,
          });
          this.fileToUpload = undefined;
          this.files = [];
          this.fileUploadStatus = 'Failed';
          // Retry upload only when file upload was interrupted after a part of it was uploaded
          if (this.uploadProgress > 0) {
            this.uploadButtonLabel = $localize`Retry`;
            this.uploadInterrupted = true;
          }
          this.uploadButtonLabel = $localize`Upload`;
          this.showMessage(JSON.stringify(error.error.detail) || $localize`Error uploading file, Retry!`);
        },
      });
  };

  /**
   * Retry/Resume File Upload Process
   */

  retryUpload = () => {
    this.fileUploadService.resumeFileUpload().subscribe({
      next: (progress) => {
        this.uploadProgress = progress;
        if (this.uploadProgress === 100) {
          // adding timeout of 1s to show progress bar for very small files that uploads instantaneously
          setTimeout(() => {
            this.fileToUpload = undefined;
            this.files = [];
            this.fileUploadStatus = 'Uploaded';
            this.dropzoneLabel = $localize`Drag and Drop your files here`;
            this.uploadButtonLabel = $localize`Upload`;
            this.showMessage($localize`Upload Complete`);
            // enable form after file uploaded successfully
            this.uploadBatchForm.enable();
            this.uploadBatchForm.reset();
            // call upload complete api
            this.fileUploadService.uploadCompelte().subscribe({
              next: (resp) => {},
              complete: () => {
                this.sidepanel.toggleNavState();
                this.getCount(
                  this.SubscriptionID,
                  this.search,
                  this.start_date,
                  this.end_date,
                  this.selectedTagValues
                );
                this.getTable(
                  this.page,
                  this.size,
                  this.activeTabName,
                  this.search,
                  this.start_date,
                  this.end_date,
                  this.SubscriptionID,
                  this.selectedTagValues
                );
              },
            });
          }, 1000);
        }
      },
      error: (error) => {
        // enable form
        this.uploadBatchForm.enable();
        this.fileUploadStatus = 'Failed';
        this.uploadButtonLabel = 'Retry';
        this.closeSidepanel();
        this.showMessage(`Couldn't resume upload, Please try again!`);
      },
    });
  };

  /**
   * Show Toast with message
   * @param msg Message to show
   * @param duration How long to show the toast {defaults to 300}
   */

  showMessage = (msg: string, duration: number = 5000): void => {
    this.matSnackbar
      .open(msg, 'OK', {
        duration,
        horizontalPosition: 'right',
        verticalPosition: 'bottom',
      })
      .onAction()
      .subscribe(() => this.matSnackbar.dismiss());
  };

  /**
   * remove selected file
   * @param event
   */
  onRemove = (event) => {
    this.files.splice(this.files.indexOf(event), 1);
    this.fileToUpload = null;
    this.onSelect(event.hasOwnProperty('addedFiles') ? event : null);
  };

  /**
   * add chip color
   */
  // chipControl = new FormControl(new Set());
  // get chip() {
  //   return this.chipControl.value;
  // }
  // toggleChip = (chip) => {
  //   {
  //     this.chip.add(chip);
  //     this.labelColor = chip;
  //   }
  // };

  /**
   * Tage color
   * @param i
   * @param item
   */
  toggleTagColor = (i, item) => {
    this.labelColor = item;
  };

  resetTagSelected = () => {
    this.labelColor = null;
  };

  /**
   * add label
   */
  addLabel = (addlabel_id, removelabel_id) => {
    this.homeService
      .addLabel(
        this.SubscriptionID,
        this.currentBatch,
        addlabel_id,
        removelabel_id
      )
      .subscribe({
        next: (resp) => {
          this.sidepanel.toggleNavState();
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID,
            this.selectedTagValues
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * get the table data for respective module
   * @param tabChangeEvent
   */

  tabChanged = (tabChangeEvent): void => {
    // console.log("tab change")
    this.tableDataLoading = true;
    this.selectedIndex = this.tabList.findIndex(
      (tab) => tab.value == tabChangeEvent.tab.textLabel
    );
    this.activeTabName = tabChangeEvent.tab.textLabel.value.toUpperCase();
    this.selectedIndex = tabChangeEvent.index;
    this.homeDataSource = [];
    this.page = 1;
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID,
      this.selectedTagValues
    );
  };

  /**
   * Remove label for batch
   * @param batch_id
   * @param removelabel_id
   * @param addlabel_id
   */
  removeLabel = (batch_id, removelabel_id, addlabel_id) => {
    this.homeService
      .addLabel(this.SubscriptionID, batch_id, addlabel_id, removelabel_id)
      .subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID,
            this.selectedTagValues
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * get batch list Table
   * @param page
   * @param size
   * @param status
   * @param search
   * @param start_date
   * @param end_date
   * @param label
   * @param subscription_id
   */
  getTable = (
    page,
    size,
    status,
    search,
    start_date,
    end_date,
    subscription_id,
    selectedTags
  ) => {
    // this.dataload = true;
    this.fetchTableData && this.fetchTableData.unsubscribe();
    this.fetchTableData = this.homeService
      .getBatchList(
        page,
        size,
        status,
        search,
        start_date,
        end_date,
        subscription_id,
        selectedTags
      )
      .subscribe({
        next: (resp) => {
          this.batchList = resp.result;
          const HOME_DATA: BatchTableList[] = this.batchList;

          this.homeDataSource = new MatTableDataSource<BatchTableList>(
            HOME_DATA
          );
          // this.getLabelList(subscription_id);
          this.page = resp.page;
          this.size = resp.page_size;
          this.totalItems = resp.total_items;
          this.totalPages = resp.total_pages;
          this.tableDataLoading = false;
          this.dataLoading = false;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.tableDataLoading = false;
          this.snackbarService.openSnackBar(`${HttpResponse.statusText}`, 'OK');
        },
      });
  };

  /**
   * get list of labels
   * @param module_slug
   */
  getLabelList = (subscription_id) => {
    this.homeService.getLabelList(subscription_id).subscribe({
      next: (resp) => {
        this.labelList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
  };

  /**
   * get list of labels
   * @param module_slug
   */
  getCount = (subscription_id, search, start_date, end_date, tags) => {
    this.homeService
      .getStatusCount(subscription_id, search, start_date, end_date, tags)
      .subscribe({
        next: (resp) => {
          this.headercount = resp.result;
          // this.headercount = {
          //   approved: 1,
          //   cancelled: 0,
          //   in_progress: 0,
          //   in_queue: 0,
          //   processed: 0,
          // };
          // go to the first tab which has the data
          // if no tab has priority
          if (!this.selectedIndex) {
            this.tabIndexWithData = this.tabList.findIndex(
              (tab) => this.headercount[tab.value] > 0
            );
            this.selectedIndex = this.tabIndexWithData == -1 ? 0: this.tabIndexWithData ;
            this.activeTabName =
              this.tabList[this.selectedIndex].value.toUpperCase();
            this.getTable(
              this.page,
              this.size,
              this.activeTabName,
              this.search,
              this.start_date,
              this.end_date,
              this.SubscriptionID,
              this.selectedTagValues
            );
          } else {
            this.activeTabName =
              this.tabList[this.selectedIndex].value.toUpperCase();
            this.getTable(
              this.page,
              this.size,
              this.activeTabName,
              this.search,
              this.start_date,
              this.end_date,
              this.SubscriptionID,
              this.selectedTagValues
            );
          }
          // this.activeTabName =
          //   this.tabList[this.tabIndexWithData].value.toUpperCase();
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * download input batch file
   * @param subscription_id
   * @param batch_id
   */
  downloadInputFile = (subscription_id, batch_id) => {
    this.homeService.downloadInputFile(subscription_id, batch_id).subscribe({
      next: (resp) => {
        window.open(resp.url, '_blank');
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
  };

  /**
   * delete comment from comment thread
   * @param comment_id
   * @param index
   */
  action = (batch_id, status?) => {
    const text =
      status == 'APPROVED'
        ? $localize`Are you sure you want to approve  ${batch_id}?
      No further processing is possible after the batch is approved.`
        : status === 'CANCELLED'
        ? $localize`Are you sure you want to cancel ${batch_id}?
      This will move the batch to 'Cancelled' tab.`
        : status === 'From_cancelled'
        ? $localize`Are you sure you want to delete ${batch_id}?
      This will delete the batch from Cancelled.`
        : $localize`Are you sure you want to delete ${batch_id}?`;

    // console.log(text);

    const primarybtn =
      status == 'APPROVED'
        ? $localize`APPROVE`
        : status == 'CANCELLED'
        ? $localize`CANCEL`
        : $localize`DELETE`;
    const secondaryBtn = $localize`Close`;

    this.dialogRef = this.dialog.open(ConfirmDialog, {
      data: {
        batch_id: batch_id,
        status: status,
        content: text,
        primarybtnName: primarybtn,
        secondaryBtnName: secondaryBtn,
        trigger: (action) => {
          if (action == 'APPROVED') {
            this.updatebatchStatus(batch_id, status);
          } else if (action == 'CANCELLED') {
            this.approveORcancel(batch_id, status);
          } else {
            this.deleteBatch(batch_id);
          }
        },
      },
    });
  };

  /**
   * Approve Batch Dialog
   * @param batch_id
   */
  openApproveBatchDialog = (batch_id) => {
    this.approveBatchDialogRef = this.dialog.open(ApproveBatchDialog, {
      data: {
        batch_id: batch_id,
        subs_id: this.SubscriptionID,
        trigger: (action) => {
          // if user approves, call the approve API
          if (action == 'approve') {
            this.updatebatchStatus(batch_id, 'APPROVED');
          }
          // else route to product detail with selected batch id
          else if (action == 'reroute') {
            this.router.navigate(['/products'], {
              queryParams: { batch_id: batch_id, sub: this.SubscriptionID },
            });
          }
        },
      },
    });
  };

  /**
   * update batch status
   * @param batch_id,status
   */
  updatebatchStatus = (batch_id, status) => {
    this.homeService
      .statusUpdate(this.SubscriptionID, batch_id, status)
      .subscribe({
        next: (resp) => {
          // this.labelList = resp.result;
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          // this.activeTabName = status.toLowerCase();
          this.selectedIndex = this.tabList.findIndex(
            (tab) => tab.value == status.toLowerCase()
          );
          this.getCount(
            this.SubscriptionID,
            this.search,
            this.start_date,
            this.end_date,
            this.selectedTagValues
          );
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID,
            this.selectedTagValues
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * detele batch
   * @param module_slug
   */
  deleteBatch = (batch_id) => {
    this.homeService.deleteBatchList(this.SubscriptionID, batch_id).subscribe({
      next: (resp) => {
        // this.labelList = resp.result;
        this.snackbarService.openSnackBar(resp.detail, 'OK');
        // this.selectedIndex = this.tabList.findIndex(
        //   // As delete feature is available only in 'in_queue'
        //   (tab) => tab.value == 'in_queue'
        // );
        this.getCount(
          this.SubscriptionID,
          this.search,
          this.start_date,
          this.end_date,
          this.selectedTagValues
        );
        this.getTable(
          this.page,
          this.size,
          this.activeTabName,
          this.search,
          this.start_date,
          this.end_date,
          this.SubscriptionID,
          this.selectedTagValues
        );
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
  };

  /**
   * Approve or cancel batch
   * @param id
   * @param module_slug
   * @param statusStr
   */
  approveORcancel = (id, statusStr) => {
    this.homeService
      .approveORcancel(this.SubscriptionID, id, statusStr)
      .subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.activeTabName = statusStr.toLowerCase();
          this.selectedIndex = this.tabList.findIndex(
            (tab) => tab.value == statusStr.toLowerCase()
          );
          this.getCount(
            this.SubscriptionID,
            this.search,
            this.start_date,
            this.end_date,
            this.selectedTagValues
          );
          // this.getTable(
          //   this.page,
          //   this.size,
          //   this.activeTabName.toUpperCase(),
          //   this.search,
          //   this.start_date,
          //   this.end_date,
          //   this.SubscriptionID
          // );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * update batch status
   * @param module_slug
   */
  createLabel = () => {
    // console.log(this.labelColor);
    // color picker values for tag bg and tag text=======
    // this.tagBgColorDecoration.rgba
    // this.tagTextColorDecoration.rgba
    // this.tagBgColorDecoration.hexWithOpacity
    // this.tagTextColorDecoration.hexWithOpacity
    // console.log('this.tagBgColorDecoration.hexWithOpacity: ', this.tagBgColorDecoration.hexWithOpacity);
    // console.log('this.tagTextColorDecoration.hexWithOpacity: ', this.tagTextColorDecoration.hexWithOpacity);
    this.createlabelFormValues = this.createLabelForm.value;
    // console.log('this.createlabelFormValues: ', this.createlabelFormValues);
    // console.log('this.createlabelFormValues.name,: ', this.createlabelFormValues.name,);
    // console.log('this.createlabelFormValues.title: ', this.createlabelFormValues.title);

    
    // ==================================================
    this.homeService
      .createLabel(
        this.SubscriptionID,
        this.createlabelFormValues.name,
        this.createlabelFormValues.title,
        this.color,
        this.textColor
      )
      .subscribe({
        next: (resp) => {
          this.labelList = resp.result;
          // 
          // console.log('createLabelForm-1: ', this.createLabelForm);
          
          // 
          this.createLabelForm.reset();
          // 
          // console.log('createLabelForm-2: ', this.createLabelForm);

          // 
          // clear the errors on reset
          Object.keys(this.createLabelForm.controls).forEach((key) => {
            this.createLabelForm.get(key).setErrors(null);
          });
          this.resetTagSelected();
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getLabelList(this.SubscriptionID);
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(JSON.stringify(HttpResponse.error.detail), 'OK');
        },
      });
  };

  /**
   * generate output file
   * @param batch_id
   */
  generateBucketFile = (batch_id, bucket) => {
    this.homeService
      .generateBucketFile(this.SubscriptionID, batch_id, bucket)
      .subscribe({
        next: (resp) => {
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID,
            this.selectedTagValues
          );
          this.snackbarService.openSnackBar(resp.detail, 'OK');
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * download batch output or insufficient data file
   * @param batch_id
   */
  downloadFile = (batch_id, bucket) => {
    this.homeService
      .downloadFile(this.SubscriptionID, batch_id, bucket)
      .subscribe({
        next: (resp) => {
          window.open(resp.url, '_blank');
          // this.matSnackbar.open(resp.detail, 'OK', {
          //   duration: 3000,
          // });
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * Asset download
   * @param url
   */
  downloadAsset = (url) => {
    window.open(url, '_blank');
  };

  /**
   * download insufficient file or output file
   * @param index
   * @param lastEdited
   * @param lastGenerated
   * @param batch_id
   * @param module_slug
   * @param download
   */
  downloadBatchFiles = (batch_id, bucket) => {
    this.getListForFile(this.SubscriptionID, batch_id, bucket);
  };

  /**
   * get List For OutputFile or insufficient data
   * @param page
   * @param size
   * @param status
   * @param search
   * @param start_date
   * @param end_date
   * @param subscription_id
   */
  toolTipMessage;
  getListForFile = (subscription_id, batch_id, bucket) => {
    // this.dataload = true;
    this.homeService
      .getSingleBatchList(subscription_id, batch_id, bucket)
      .subscribe({
        next: (resp) => {
          this.tableDataLoading = false;
          this.dataLoading = false;

          const lastEdited = resp.last_edited_at;
          const lastGenerated = resp.last_generated_at;
          const download = resp.download;
          const batch_id = resp.batch_id;
          // console.log(lastEdited, lastGenerated, download, batch_id);
          if (
            download?.in_progress === true &&
            download?.percent_completed !== null
          ) {
            this.toolTipMessage = $localize`Processing... please wait`;
          }
          if (download == null) {
            this.generateBucketFile(batch_id, bucket);
          } else if (download && download.url == null) {
            this.getTable(
              this.page,
              this.size,
              this.activeTabName,
              this.search,
              this.start_date,
              this.end_date,
              this.SubscriptionID,
              this.selectedTagValues
            );
            this.matSnackbar.open($localize`Processing... please wait`, 'OK', {
              duration: 3000,
            });
          } else if (
            download &&
            download.url &&
            download.percent_completed == 100 &&
            new Date(lastEdited).getTime() > new Date(lastGenerated).getTime()
          ) {
            this.generateBucketFile(batch_id, bucket);
          } else if (
            download &&
            download.url &&
            download.percent_completed == 100 &&
            new Date(lastEdited).getTime() < new Date(lastGenerated).getTime()
          ) {
            this.downloadFile(batch_id, bucket);
          } else {
            // console.log('errrr');
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.tableDataLoading = false;
          this.snackbarService.openSnackBar(`${HttpResponse.statusText}`, 'OK');
        },
      });
  };

  getLog = (category, category_id) => {
    this.logsLoading = true;
    this.homeService
      .getLog(this.SubscriptionID, category, category_id)
      .subscribe({
        next: (res) => {
          this.logsLoading = false;
          this.logList = res;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.logsLoading = false;
          this.logList = 'No logs found';
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * Get table data on pagination
   * @param identifier
   * @param tabName
   */
  onPaginateChange = (identifier) => {
    this.page = identifier.pageIndex + 1;
    this.size = identifier.pageSize;
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID,
      this.selectedTagValues
    );
  };

  /**
   * Update ETA date
   * @param batch
   * @param ev
   */
  modifyEta = (batch, ev) => {
    this.homeService
      .modifyETA(
        this.SubscriptionID,
        batch,
        new Date(ev).toISOString().split('.')[0] + 'Z'
      )
      .subscribe({
        next: (res) => {
          this.logsLoading = false;
          this.snackbarService.openSnackBar(res.detail, 'OK');
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID,
            this.selectedTagValues
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.logsLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  // ///////////////

  get references(): FormArray {
    return this.uploadBatchForm.get('reference') as FormArray;
  }

  addReferences = () => {
    if (this.references.length >= 10) {
      this.refFlag = true;

      // this.snackbarService.openSnackBar('Maximum of 10 References can be added', '');
      return;
    }

    this.references.push(this.fb.control('', [Validators.maxLength(1000)]));
  };
  removeReferences = (i: number) => {
    if (this.references.length <= 1) return;
    this.refFlag = false;
    this.references.removeAt(i);
  };

  getLengthValidity = (i) => {
    return (<FormArray>this.uploadBatchForm.get('reference')).controls[
      i
    ].hasError('maxlength');
  };

  getContentValidity = (i) => {
    return (<FormArray>this.uploadBatchForm.get('reference')).controls[
      i
    ].hasError('pattern');
  };

  getTest = (i) => {
    return (<FormArray>this.uploadBatchForm.get('reference')).invalid;
  };

  checkRefFieldEmpty = () => {
    return (<FormArray>this.uploadBatchForm.get('reference')).controls.some(
      (refField) => {
        return refField.value === '' || refField.value === null;
      }
    );
    // return (<FormArray>this.uploadBatchForm.get('reference')).controls[i].value ? false : true;
  };

  moreIconVisibility = (references) => {
    if (references) {
      if (references[0]) return true;
    }
    return false;
  };


  /**
   * export all batches
   */
  exportAllBatches = () => {
    timer(1, 3000)
      .pipe(
        switchMap(() => this.http.get('/api/subscriptions/'+this.SubscriptionID+'/batches/export')),
        takeUntil(this.stopPolling$)
      )
      .subscribe((data) => {
        this.exportDownloadUrl = data['download_url'];
        if(this.exportDownloadUrl) {
          window.open(this.exportDownloadUrl, '_blank')
          this.stopPolling$.next(true)
        } else {
          this.snackbarService.openSnackBar(data['message'] || 'File export in progress, Please wait.', 'OK');
        }

      });
  }

}

