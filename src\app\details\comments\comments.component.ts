import {
  Component,
  OnInit,
  HostListener,
  <PERSON>Child,
  ElementRef,
  Inject,
} from '@angular/core';
import { CommentsService } from 'src/app/services/comments.service';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { interval as observableInterval } from 'rxjs';
import { takeWhile, scan, tap } from 'rxjs/operators';
import 'quill-mention';
import { QuillEditorComponent } from 'ngx-quill';
import { DOCUMENT } from '@angular/common';
import { FileUploadService } from '../../services/file-upload.service';
import { ViewEncapsulation } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ConfirmDialog } from 'src/app/_dialogs/confirm-dialog/confirm-dialog.component';
import { SnackbarService } from '../../services/snackbar.service';
interface User {
  name: string;
  username: string;
}
@Component({
  selector: 'app-comments',
  templateUrl: './comments.component.html',
  styleUrls: ['./comments.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class CommentsComponent implements OnInit {
  @HostListener('document:fullscreenchange', ['$event'])
  @HostListener('document:webkitfullscreenchange', ['$event'])
  @HostListener('document:mozfullscreenchange', ['$event'])
  @HostListener('document:MSFullscreenChange', ['$event'])
  fullScreenChange() {
    this.isFullscreen = !this.isFullscreen;
    this.isEditFullscreen = !this.isEditFullscreen;
  }
  @ViewChild(QuillEditorComponent) editor: QuillEditorComponent;
  @ViewChild('fullScreen') divRef: ElementRef;
  @ViewChild('fullScreenEdit') editFullScreen: ElementRef;

  /** editor ngx quill */

  content = '';
  editedContent = '';
  matContent = '';
  selectedUsers: any[] = [];
  selectedUsersEdited: any[] = [];

  editorStyle = {
    maxHeight: '150px',
    height: '100px',
  };

  modules = {
    mention: {
      allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
      onSelect: (item, insertItem) => {
        const editor = this.editor.quillEditor;
        // this.selectedUsers.push(item.id);
        insertItem(item);
        // necessary because quill-mention triggers changes as 'api' instead of 'user'
        editor.insertText(editor.getLength() - 1, '', 'user');
      },
      source: (searchTerm, renderList) => {
        const values = this.mentionUsers;
        if (searchTerm.length === 0) {
          renderList(values, searchTerm);
        } else {
          const matches = [];

          values.forEach((entry) => {
            if (
              entry.value.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1
            ) {
              matches.push(entry);
            }
          });
          renderList(matches, searchTerm);
        }
      },
    },
  };
  editModuleconfig = {
    mention: {
      allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
      onSelect: (item, insertItem) => {
        const editor = this.editor.quillEditor;
        // this.selectedUsersEdited.push(item.id);
        insertItem(item);
        // necessary because quill-mention triggers changes as 'api' instead of 'user'
        editor.insertText(editor.getLength() - 1, '', 'user');
      },
      source: (searchTerm, renderList) => {
        const values = this.mentionUsers;
        if (searchTerm.length === 0) {
          renderList(values, searchTerm);
        } else {
          const matches = [];

          values.forEach((entry) => {
            if (
              entry.value.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1
            ) {
              matches.push(entry);
            }
          });
          renderList(matches, searchTerm);
        }
      },
    },
  };
  isFullscreen: boolean = false;
  isEditFullscreen: boolean = false;
  subscriptionId;
  page: number = 1;
  size: number = 10;
  batchId;
  rowId;
  commentsListForType;
  totalItems;
  totalPage;
  dataLoading: boolean;
  commentsLoading: boolean;
  commentsList: any[] = [];
  search;
  searchedItem: string = '';
  activeCommentIndex = 1;
  selectedCommentId;
  userNameList: string[] = [];
  comments: any[] = [];
  taggedUsersByUsername: any[] = [];
  taggedUsersByName: any[] = [];
  taggedUsers: any[] = [];
  mentionUsers: any[];
  queryParams;
  commentThread: any[] = [];
  threadPage: number = 1;
  threadSize: number = 10;
  threadTotalItems: number;
  threadTotalPage: number;
  commentIsResolved: boolean;
  row: number;
  commentThreadTitle;
  userData;
  text = ``;
  loading = false;
  choices: User[] = [];
  selectedValForToggle;
  originFrom;
  row_id;
  bucket;
  categoryId;
  currentInput;
  editorPlaceholder = $localize`Comment or mention others with @`;
  dialogRef: MatDialogRef<ConfirmDialog, any>;
  isImgUpload: boolean = false;
  isImgUploadEdit: boolean = false;
  editorRef;
  editorRefEdit;
  @ViewChild('fileInput') imgFileInput;
  @ViewChild('fileInputEdit') imgFileInputEdit;
  isAllowAttachment: boolean = false;
  isCommentChanged: boolean = false;
  contentWithoutTaggedUsers: string = '';
  editedContentWithoutTaggedUsers: string = '';

  constructor(
    @Inject(DOCUMENT) private document: any,
    private router: Router,
    private route: ActivatedRoute,
    private commentsService: CommentsService,
    private matSnackbar: MatSnackBar,
    private fileUploadService: FileUploadService,
    private dialog: MatDialog,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit(): void {
    this.dataLoading = true;
    this.commentsLoading = true;
    // get batch id if any from route
    this.route.queryParams.subscribe((params: Params) => {
      this.batchId = params.batch_id;
      this.originFrom = params.origin;
      this.row_id = params.row_id;
      this.bucket = params.bucket;
      // toggle button as per the batch/sku id or no id
      if (this.batchId) {
        this.commentsListForType = 'batch';
        this.categoryId = this.batchId;
      } else if (this.row_id) {
        this.commentsListForType = 'row';
        this.categoryId = this.row_id;
      } else {
        this.commentsListForType = 'batch';
        this.categoryId = null;
      }
    });
    // add subscription id from local storage in url as param
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { sub: this.subscriptionId },
      queryParamsHandling: 'merge', // remove to replace all query params by provided
    });
    // get user data from local storage
    if (localStorage.getItem('user')) {
      this.userData = JSON.parse(localStorage.getItem('user'));
    }
    // get user name List
    this.getUserNameList();
    // get comments list
    this.getCommentsList(
      this.subscriptionId,
      this.page,
      this.size,
      this.commentsListForType,
      this.categoryId,
      this.searchedItem,
      false
    );
  }

  files = [];
  filesEdit = [];
  uploadedFile;
  uploadCheck: boolean = false;
  editUploadcheck: boolean = false;
  isEditFileRemoved: boolean = false;
  attachmentID: number;

  onFileSelected = (event, mode) => {
    if (
      event.target.files.length > 0 &&
      event.target.files[0].size <= 10000000
    ) {
      if (mode === 'create') {
        this.uploadCheck = true;
        this.uploadedFile = event.target.files[0];
        this.files.push(this.uploadedFile);
      }

      if (mode === 'edit') {
        this.editUploadcheck = true;
        this.filesEdit.push(event.target.files[0]);
        this.isAllowAttachment = false;
        this.isCommentChanged = true;
      }
    } else {
      this.matSnackbar.open('please upload file size less than 10MB', 'OK', {
        duration: 3000,
      });
    }
  };

  clear = (index) => {
    this.files.splice(index, 1);
    // this.filesEdit.splice(index, 1);
  };

  clearEditAttachment(comment, index) {
    this.filesEdit = [];
    this.isEditFileRemoved = true;
    this.isAllowAttachment = true;
    this.isCommentChanged = false;
    this.attachmentID = comment['attachment']['attachment_id'];
  }
  placeholder;
  /**
   * On button toggle change
   * @param val
   */
  onButtonToggle = (val) => {
    if (val === 'batch') {
      this.categoryId = this.batchId;
    } else if (val == 'row') {
      this.categoryId = this.row_id;
    } else {
      this.categoryId = null;
    }
    this.content = '';
    this.commentsListForType = val;
    this.searchedItem = '';
    this.search = '';
    this.commentsList = [];
    this.commentThread = [];
    this.dataLoading = true;
    this.page = 1;
    this.threadPage = 1;
    this.getCommentsList(
      this.subscriptionId,
      this.page,
      this.size,
      this.commentsListForType,
      this.categoryId,
      this.searchedItem,
      false
    );
  };

  /**
   * reset search keyword
   */
  resetSearch = () => {
    this.searchedItem = '';
    this.search = '';
    this.getCommentsList(
      this.subscriptionId,
      this.page,
      this.size,
      this.commentsListForType,
      this.categoryId,
      this.searchedItem,
      false
    );
  };

  /**
   * get comment list from searched item in search bar
   * @param q
   */
  getSearchedItem = (q) => {
    this.searchedItem = q.trim();
    this.getCommentsList(
      this.subscriptionId,
      this.page,
      this.size,
      this.commentsListForType,
      this.categoryId,
      this.searchedItem,
      false
    );
  };

  /**
   * get active/highlighted comment index from side div
   * @param i
   * @param id
   */
  getActiveCommentIndex = (i, comment) => {
    this.activeCommentIndex = i + 1;
    // empty previously tagged names
    this.selectedCommentId = comment.category_id;
    this.commentIsResolved = comment.is_resolved;
    // show comment thread loader
    this.commentsLoading = true;
    this.getCommentThread(
      this.subscriptionId,
      (this.threadPage = 1),
      this.threadSize,
      this.commentsListForType,
      this.selectedCommentId,
      this.searchedItem,
      true
    );
  };

  /**
   * get list of commemts to display on side div
   * @param subs_id
   * @param page
   * @param size
   * @param category
   * @param category_id
   * @param q
   * @param comment_thread
   */
  getCommentsList = (
    subs_id,
    page,
    size,
    category,
    category_id,
    q,
    comment_thread
  ) => {
    // reset active comment index to 1
    this.activeCommentIndex = 1;
    this.commentsService
      .getCommentsList(
        subs_id,
        page,
        size,
        category,
        category_id,
        q,
        comment_thread
      )
      .subscribe({
        next: (resp) => {
          this.dataLoading = false;
          this.commentsList = [];
          this.commentsList = [...this.commentsList, ...resp.result];
          this.page = resp.page;
          this.size = resp.page_size;
          this.totalItems = resp.total_items;
          this.totalPage = resp.total_pages;
          this.selectedCommentId = this.commentsList[0].category_id;
          this.commentIsResolved = this.commentsList[0].is_resolved;
          this.getCommentThread(
            subs_id,
            this.threadPage,
            this.threadSize,
            category,
            this.selectedCommentId,
            this.searchedItem,
            true
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * get comment thread for respective sku/batch comment
   * @param subs_id
   * @param page
   * @param size
   * @param category
   * @param batch_or_row_id
   * @param q
   * @param comment_thread
   */
  getCommentThread = (
    subs_id,
    page,
    size,
    category,
    batch_or_row_id,
    q,
    comment_thread
  ) => {
    this.commentsService
      .getCommentsList(
        subs_id,
        page,
        size,
        category,
        batch_or_row_id,
        q,
        comment_thread
      )
      .subscribe({
        next: (resp) => {
          this.commentsLoading = false;
          // this.commentThread = [...this.commentThread, ...resp.result];
          // this.commentThread = resp.result;

          this.threadPage = resp.page;
          this.threadSize = resp.page_size;
          this.threadTotalItems = resp.total_items;
          this.threadTotalPage = resp.total_pages;
          this.commentThreadTitle = resp.title;
          // if difference between total pages capacity and length of comment thread list is less than one page capacity
          // replace the last set of data with incoming data
          // using splice to replace
          if (
            this.threadPage * this.threadSize - this.commentThread.length <
            this.threadSize
          ) {
            this.commentThread.splice(
              (this.threadPage - 1) * this.threadSize,
              this.commentThread.length -
                (this.threadPage - 1) * this.threadSize,
              ...resp.result
            );
          } else {
            this.commentThread = [...this.commentThread, ...resp.result];
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.commentsLoading = true;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * post comment
   * @param comment comment
   * @param i index
   */
  postComment = (comment, user, file) => {
    let users = [];
    const IDs = [];
    this.contentWithoutTaggedUsers = '';
    let obj = {
      comment: comment,
      tagged_users: users,
      attachment_name: file.length > 0 ? file[0].name : file,
      file_hash_ids: IDs,
    };
    const { ops } = this.editor.quillEditor.getContents();
    const result = ops.reduce((acc, { insert }) => {
      if (typeof insert === 'string') {
      } else {
        if (insert.hasOwnProperty('mention')) {
          users.push(insert.mention.id);
        }
        if (insert.hasOwnProperty('image')) {
          const test = insert.image.match(/attachments\/(.*)\//);
          IDs.push(test[1]);
        }
      }

      return acc;
    }, '');

    // remove duplicate tagged users [allowed in editor]
    obj.tagged_users = [...new Set(users)];
    this.commentsService
      .postComment(
        this.subscriptionId,
        this.commentsListForType,
        this.selectedCommentId,
        obj
      )
      .subscribe({
        next: (resp) => {
          this.files = [];
          this.selectedUsers = [];
          this.content = '';

          this.snackbarService.openSnackBar(resp.detail, 'OK');
          if (resp['signed_url'] === '') {
            this.updateSidepanelAndThread();
            return;
          }
          this.fileUploadService
            .getSessionURI(resp.signed_url, file[0])
            .subscribe({
              next: (progress) => {
                this.updateSidepanelAndThread();
                this.fileUploadService
                  .uploadCompelte(true, resp.comment_id, this.subscriptionId)
                  .subscribe({
                    next: (resp) => {},
                  });
              },
              error: (HttpResponse: HttpErrorResponse) => {
                this.dataLoading = false;
                this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
              },
            });
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * get user list for tagging in comments
   */
  getUserNameList = () => {
    this.commentsService.getUserNamesToTag(this.subscriptionId).subscribe({
      next: (resp) => {
        this.mentionUsers = resp.result.map((obj) => ({
          id: obj.username,
          value: obj.name,
        }));
      },
    });
  };

  /**
   * resolve comment
   */
  resolveComment = () => {
    this.commentsService
      .resolveComment(
        this.subscriptionId,
        this.commentsListForType,
        this.selectedCommentId
      )
      .subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          // mark the selected batch/sku comments resolved as true
          this.commentsList.filter(
            (comment) => comment.category_id == this.selectedCommentId
          )[0].is_resolved = true;
          this.commentIsResolved = true;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * event fired when comment preview section is scrolled
   * fetching new pages
   */
  onScrollDown() {
    this.page++;
    if (this.page <= this.totalPage) {
      this.commentsService
        .getCommentsList(
          this.subscriptionId,
          this.page,
          this.size,
          this.commentsListForType,
          this.categoryId,
          this.searchedItem,
          false
        )
        .subscribe({
          next: (resp) => {
            this.commentsList = [...this.commentsList, ...resp.result];
            this.page = resp.page;
            this.size = resp.page_size;
            this.totalItems = resp.total_items;
            this.totalPage = resp.total_pages;
          },
        });
    }
  }

  onUp = () => {
    if (this.threadPage > 1) {
      this.threadPage--;
    }
    this.commentsService
      .getCommentsList(
        this.subscriptionId,
        this.threadPage,
        this.threadSize,
        this.commentsListForType,
        this.selectedCommentId,
        this.searchedItem,
        true
      )
      .subscribe({
        next: (resp) => {
          this.commentsLoading = false;
          this.commentThread = resp.result;
          this.threadPage = resp.page;
          this.threadSize = resp.page_size;
          this.threadTotalItems = resp.total_items;
          this.threadTotalPage = resp.total_pages;
          // if all the data on the page is deleted/lost
          if (this.commentThread.length == 0) {
            this.onUp();
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.commentsLoading = true;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * event fired when comment thread section is scrolled
   * fetching new pages
   */
  onCommentThreadScroll = () => {
    // check if comment thread length is equal to total item capacity of pages
    // or check if total item size > total item capacity of pages
    // increment pageSize by 1
    if (this.threadTotalItems > this.commentThread.length) {
      if (this.commentThread.length == this.threadSize * this.threadPage) {
        this.threadPage++;
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.selectedCommentId,
          this.searchedItem,
          true
        );
      } else {
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.selectedCommentId,
          this.searchedItem,
          true
        );
      }
    }
  };

  scrollToTop(el) {
    const duration = 600;
    const interval = 5;
    const move = (el.scrollTop * interval) / duration;
    observableInterval(interval)
      .pipe(
        scan((acc, curr) => acc - move, el.scrollTop),
        tap((position) => (el.scrollTop = position)),
        takeWhile((val) => val > 0)
      )
      .subscribe();
  }

  /**
   * used to highlight existing tagged users in textarea
   * @param comment
   * @returns
   */
  getSelectedChoices = (comment): User[] => {
    return comment.tagged_users;
  };

  /**
   * edit comment
   * @param comment
   */
  editComment = (comment) => {
    this.editedContentWithoutTaggedUsers = '';
    this.isCommentChanged = false;
    this.attachmentID = null;
    this.selectedUsersEdited = comment.tagged_users;
    comment['edited_text'] = comment.text;
    this.isEditFileRemoved = false;
    this.filesEdit = [];
    this.editUploadcheck = false;
    this.isEditFileRemoved = false;
    comment['attachment'].hasOwnProperty('name')
      ? (this.isAllowAttachment = false)
      : (this.isAllowAttachment = true);
    // store original values
    // comment.edited_text = comment.complete_text;
    // comment.edited_tagged_users = comment.tagged_users;
    // deactivate all other comment edits
    this.commentThread
      .filter((item) => item.comment_id != comment.comment_id)
      .forEach((row) => {
        row.editable = false;
      });
  };

  /**
   * update existing comment
   * @param comment
   */
  updateComment = (comment, user, file) => {
    const addedUsers = []; //only added users
    const IDs = [];
    this.editedContentWithoutTaggedUsers = '';
    let obj = {
      comment: comment['edited_text'],
      tagged_users: addedUsers, //default
      attachment_name: file.length > 0 ? file[0].name : file,
      remove_attachment_id: this.attachmentID,
      file_hash_ids: IDs,
      remove_tagged_users: [], // default
    };
    if (obj.comment == null) {
      obj.comment = '';
    }

    const { ops } = this.editor.quillEditor.getContents();
    const holdEditorUsers = [];

    // check for tagged users
    const result = ops.reduce((acc, { insert }) => {
      if (typeof insert === 'string') {
      } else {
        if (insert.hasOwnProperty('mention')) {
          const existingUser = this.selectedUsersEdited.find(
            (that) => insert.mention.id === that.username
          );
          if (!existingUser) {
            addedUsers.push(insert.mention.id);
          }
          holdEditorUsers.push(insert.mention.id);
        }
        if (insert.hasOwnProperty('image')) {
          const test = insert.image.match(/attachments\/(.*)\//);
          IDs.push(test[1]);
        }
      }

      return acc;
    }, '');

    // check for removed users
    if (this.selectedUsersEdited.length > 0) {
      obj.remove_tagged_users = this.selectedUsersEdited
        .filter((each) => !holdEditorUsers.includes(each.username))
        .map((each) => each.username);
    }

    // remove duplicate tagged users [allowed in editor]
    obj.tagged_users = [...new Set(addedUsers)];
    this.commentsService
      .updateComment(this.subscriptionId, comment.comment_id, obj)
      .subscribe({
        next: (resp) => {
          this.filesEdit = [];
          this.selectedUsersEdited = [];
          this.attachmentID = null;
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          comment.editable = false;
          if (!resp['attachments'].hasOwnProperty('signed_url')) {
            this.updateSidepanelAndThread('edit');
            return;
          }
          if (
            resp['attachments'].hasOwnProperty('signed_url') &&
            resp['attachments']['signed_url'] != ''
          ) {
            this.updateSidepanelAndThread('edit');
            this.fileUploadService
              .getSessionURI(resp['attachments']['signed_url'], file[0])
              .subscribe({
                next: (progress) => {
                  // this.updateSidepanelAndThread('edit');
                  this.fileUploadService
                    .uploadCompelte(
                      true,
                      comment.comment_id,
                      this.subscriptionId
                    )
                    .subscribe({
                      next: (resp) => {},
                    });
                },
                error: (HttpResponse: HttpErrorResponse) => {
                  this.dataLoading = false;
                  this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
                    duration: 3000,
                  });
                },
              });
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * delete comment from comment thread
   * @param comment_id
   * @param index
   */
  deleteComment = (comment_id, index) => {
    const primarybtn = $localize`Delete`;
    const secondaryBtn = $localize`Close`;
    const text = $localize`Are you sure you want to delete this comment?`;
    const status = 'DELETE COMMENTS';
    this.dialogRef = this.dialog.open(ConfirmDialog, {
      data: {
        content: text,
        status: status,
        primarybtnName: primarybtn,
        secondaryBtnName: secondaryBtn,
        batch_id: comment_id,
        trigger: (action) => {
          this.confirmDelete(comment_id, index);
        },
      },
    });
  };

  confirmDelete(comment_id, index) {
    this.dialogRef.afterClosed().subscribe((discard) => {
      if (discard) {
        this.commentsService
          .deleteComment(this.subscriptionId, comment_id)
          .subscribe({
            next: (resp) => {
              this.commentThread.splice(index, 1);
              this.snackbarService.openSnackBar(resp.detail, 'OK');
              this.updateSidepanelAndThread();
            },
            error: (HttpResponse: HttpErrorResponse) => {
              this.dataLoading = false;
              this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
            },
          });
      }
      this.dialogRef = null;
    });
  }

  /**
   * tag removal event in comment thread
   * @param e
   */
  taggedUserRemovedInCommentEdit = (e) => {};

  openFullscreen(mode) {
    let elem;
    if (mode && mode === 'create') {
      elem = this.divRef.nativeElement;
    }
    if (mode && mode === 'edit') {
      elem = this.editFullScreen.nativeElement;
    }

    if (elem.requestFullscreen) {
      elem.requestFullscreen();
    } else if (elem.msRequestFullscreen) {
      /* IE/Edge */
      elem.msRequestFullscreen();
    } else if (elem.mozRequestFullScreen) {
      /* Firefox */
      elem.mozRequestFullScreen();
    } else if (elem.webkitRequestFullscreen) {
      /* Chrome, Safari and Opera */
      elem.webkitRequestFullscreen();
    }
  }

  closeFullscreen() {
    if (this.document.exitFullscreen) {
      this.document.exitFullscreen();
    } else if (this.document.mozCancelFullScreen) {
      /* Firefox */
      this.document.mozCancelFullScreen();
    } else if (this.document.webkitExitFullscreen) {
      /* Chrome, Safari and Opera */
      this.document.webkitExitFullscreen();
    } else if (this.document.msExitFullscreen) {
      /* IE/Edge */
      this.document.msExitFullscreen();
    }
  }

  toggleFullscreen(isActive, mode) {
    isActive ? this.openFullscreen(mode) : this.closeFullscreen();
  }

  handler(mode, comment?) {
    if (mode === 'create') {
      this.postComment(this.content, this.selectedUsers, this.files);
    }
    if (mode === 'edit') {
      this.updateComment(comment, this.selectedUsersEdited, this.filesEdit);
    }
  }

  updateSidepanelAndThread(mode?) {
    this.getCommentThread(
      this.subscriptionId,
      (this.threadPage = 1),
      this.threadSize,
      this.commentsListForType,
      this.selectedCommentId,
      this.searchedItem,
      true
    );
    if (this.commentsListForType === 'batch') {
      this.categoryId = this.batchId;
    } else if (this.commentsListForType === 'row') {
      this.categoryId = this.row_id;
    } else {
      this.categoryId = null;
    }
    this.commentsService
      .getCommentsList(
        this.subscriptionId,
        this.page,
        this.size,
        this.commentsListForType,
        this.categoryId,
        this.searchedItem,
        false
      )
      .subscribe({
        next: (resp) => {
          this.commentsList = resp.result;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  }

  onEditorFocus(event) {
    // event.editor.theme.modules.toolbar.container.style.visibility = "visible";
  }

  onEditorBlur(event) {
    // event.editor.theme.modules.toolbar.container.style.visibility = "hidden";
  }

  onContentChange(e, mode, comment?) {
    if (mode === 'edit') {
      this.isCommentChanged = true;
    }
    if (e.html) {
      let result = this.extractContent(e.html);
      // remove tagged users from string and find the remaining content
      if (mode == 'create') {
        this.contentWithoutTaggedUsers = this.removeFromString(
          this.mentionUsers.map((user) => '@' + user.value),
          result
        )
          .trim()
          .replace('@', '');
      } else {
        this.editedContentWithoutTaggedUsers = this.removeFromString(
          this.mentionUsers.map((user) => '@' + user.value),
          result
        )
          .trim()
          .replace('@', '');
      }
      if (result === '') {
        if (mode === 'create' && !this.content.includes('<img')) {
          this.content = '';
        }
        if (mode === 'edit' && !comment.edited_text.includes('<img')) {
          comment.edited_text = '';
        }
      }
    }
  }

  removeFromString(words, str) {
    return words.reduce((result, word) => result.replaceAll(word, ''), str);
  }

  extractContent(value) {
    let span = document.createElement('div');
    span.innerHTML = value;
    return span.textContent.trim() || span.innerText.trim();
  }

  startEditorLoader(mode?) {
    mode && mode === 'edit'
      ? (this.isImgUploadEdit = true)
      : (this.isImgUpload = true);
  }

  stopEditorLoader(mode?) {
    mode && mode === 'edit'
      ? (this.isImgUploadEdit = false)
      : (this.isImgUpload = false);
  }

  getEditorInstance(editorInstance: any, mode?) {
    if (mode && mode === 'edit') {
      this.editorRefEdit = editorInstance;
      this.attachPasteListener('edit');
    } else {
      this.editorRef = editorInstance;
      this.attachPasteListener('create');
    }
  }

  attachPasteListener(mode) {
    if (mode === 'create') {
      this.editorRef.root.addEventListener('paste', (e) => {
        const clipboardData = e.clipboardData || (<any>window).clipboardData;
        // const isImage = clipboardData.types.length && clipboardData.types.join('').includes('Files');
        if (clipboardData.files.length === 0) {
          return;
        } else if (clipboardData.files[0].type.startsWith('image/')) {
          e.preventDefault();
          const file = clipboardData.files[0];
          this.insertImage('', 'copy', file);
        }
      });
    }
    if (mode === 'edit') {
      this.editorRefEdit.root.addEventListener('paste', (e) => {
        const clipboardData = e.clipboardData || (<any>window).clipboardData;
        if (clipboardData.files.length === 0) {
          return;
        } else if (clipboardData.files[0].type.startsWith('image/')) {
          e.preventDefault();
          const file = clipboardData.files[0];
          this.insertImage('', 'edit', file);
        }
      });
    }
  }

  insertImage(event, mode?, file?) {
    if (event) {
      file = (event.target as HTMLInputElement).files[0];
    }
    if (file && file.size <= 10000000) {
      this.startEditorLoader(mode);
      this.uploadImage(file, mode);
    }
  }

  uploadImage(file?: File, mode?) {
    this.commentsService
      .uploadImage(file, mode, this.subscriptionId)
      .subscribe({
        next: (res) => {
          if (mode && mode === 'edit') {
            this.imgFileInputEdit.nativeElement.value = '';
          } else {
            this.imgFileInput.nativeElement.value = ''; // allow to select the same file
          }
          this.embedImage(res['redirect_url'], mode);
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.imgFileInput.nativeElement.value = ''; // allow to select the same file
          this.imgFileInputEdit.nativeElement.value = '';
          this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
            duration: 3000,
          });
        },
      });
  }

  embedImage(url: string, mode?) {
    if (!this.editorRef && !this.editorRefEdit) {
      return;
    }
    if (mode && mode === 'edit') {
      const range = this.editorRefEdit.getSelection();
      this.editorRefEdit.insertEmbed(range, 'image', url, 'user');
    } else {
      const range = this.editorRef.getSelection();
      this.editorRef.insertEmbed(range, 'image', url, 'user');
    }
    this.stopEditorLoader(mode);
  }

  onCancelEdit(comment) {
    comment.editable = false;
    this.attachmentID = null;
  }
}
