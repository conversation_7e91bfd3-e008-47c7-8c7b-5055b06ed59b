import { ThrowStmt } from '@angular/compiler';
import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { HomeService } from 'src/app/services/home.service';
import { HttpErrorResponse } from '@angular/common/http';
import { SnackbarService } from '../../services/snackbar.service';

@Component({
  selector: 'app-approve-batch',
  templateUrl: './approve-batch.component.html',
  styleUrls: ['./approve-batch.component.scss'],
})
export class ApproveBatchDialog implements OnInit {
  batchId;
  subsId;
  batchData;
  constructor(
    @Inject(MAT_DIALOG_DATA) private data: any,
    public discardDialogRef: MatDialogRef<ApproveBatchDialog, any>,
    private homeService: HomeService,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit(): void {
    this.batchId = this.data.batch_id;
    this.subsId = this.data.subs_id;
    this.homeService
      .getBatchStatusBeforeApproval(this.subsId, this.batchId)
      .subscribe({
        next: (resp) => {
          this.batchData = resp.result;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  }

  recordUserAction = (action) => {
    this.data.trigger(action);
    this.discardDialogRef.close(true);
  };
}
