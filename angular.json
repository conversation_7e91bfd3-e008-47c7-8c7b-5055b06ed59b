{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"moduleR2E": {"i18n": {"sourceLocale": {"code": "en-US", "baseHref": "/en/"}, "locales": {"ja": {"translation": "src/locale/messages.ja.xlf", "baseHref": "/ja/"}}}, "projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "./webpack.config.js"}, "outputPath": "dist/moduleR2E", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "localize": true, "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles/styles.scss", "src/styles/mat-theme.scss", "./node_modules/quill-mention/dist/quill.mention.min.css"], "scripts": ["node_modules/chart.js/dist/chart.min.js", "./node_modules/quill/dist/quill.min.js"], "i18nMissingTranslation": "ignore"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "outputHashing": "all"}, "stag": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stag.ts"}], "outputHashing": "all"}, "development": {"localize": false, "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "all"}, "development-ja": {"localize": ["ja"], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "all", "outputPath": "dist/moduleR2E/ja/"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "moduleR2E:build", "proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"browserTarget": "moduleR2E:build:production"}, "development": {"browserTarget": "moduleR2E:build:development"}, "ja": {"browserTarget": "moduleR2E:build:development-ja"}, "en": {"browserTarget": "moduleR2E:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "moduleR2E:build", "outFile": "messages.en.xlf", "outputPath": "src/locale"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}}}}}, "defaultProject": "moduleR2E"}