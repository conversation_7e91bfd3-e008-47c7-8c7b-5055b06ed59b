var path = require("path");
var CompressionPlugin = require("compression-webpack-plugin");
const webpack = require("webpack");

module.exports = {
  entry: {},
  output: {
    path: path.resolve(__dirname, "dist"),
    filename: "[name].[fullhash].js",
  },
  plugins: [
    // compresesses the files to gzip
    new CompressionPlugin(),
    // Filter out the moment locales to reduce bundle size
    // Locales that should be included MUST be added to the project, otherwise they won't be available for use)
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/locale$/,
        contextRegExp: /moment$/
      })
  ],
};
