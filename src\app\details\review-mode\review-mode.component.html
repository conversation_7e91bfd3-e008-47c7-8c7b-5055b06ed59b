<div class="wrapper" fxFlex="100">
  <div class="filter-container" fxLayout="column" fxLayoutAlign="start space-between">
    <div class="filter-head" fxLayoutAlign="space-between start">
      <div fxLayout="row" fxLayoutGap="10px" class="search-container" style="display: flex; flex-basis: 100%">
        <mat-chip-list #chipList> </mat-chip-list>
        <mat-form-field fxFlex="20" appearance="none" class="search-filter">
          <input id="search" matInput i18n-placeholder placeholder="Search MPN, Batch ID, Row ID"
            [matChipInputFor]="chipList" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="addQuery($event)" [(ngModel)]="search" />
          <mat-icon matPrefix class="search-icon" (click)="addQuery($event)">search</mat-icon>
          <mat-icon matSuffix class="remove-icon" (click)="resetSearch()" *ngIf="search">close</mat-icon>
          <mat-chip-list #chipList> </mat-chip-list>
        </mat-form-field>

        <div class="btn-container" fxLayout="row" fxLayoutGap="10px">
          <mat-form-field appearance="none" class="date-range-filter">
            <mat-select [(value)]="selected" (selectionChange)="getDataForDate($event.value)">
              <div *ngFor="let option of datePickerOptions">
                <!-- Recent -->
                <mat-option *ngIf="option.value === 'recent'" value="recent">
                  <div fxLayout="column" class="range-category" *ngIf="option.value === 'recent'" fxLayout="row">
                    <span i18n> Recent</span> &nbsp;
                    <span class="date-range"> </span>
                  </div>
                </mat-option>
                <!-- Last Week / Month / Quarter -->
                <mat-option [value]="option.value" *ngIf="
                    option.value !== 'recent' && option.value !== 'custom_range'
                  ">
                  <div fxLayout="column" class="range-category">
                    {{ option.display }}
                    <span class="date-range">
                      {{ option.start_date | date: "mediumDate" }} -
                      {{ currentDate | date: "mediumDate" }}</span>
                  </div>
                </mat-option>
                <!-- Custom range  -->
                <mat-option *ngIf="option.value === 'custom_range'" value="custom_range" (click)="picker.open()">
                  <div fxLayout="row" class="range-category" fxLayoutAlign="start center">
                    <span i18n>Custom Range</span>
                    <span class="date-range" *ngIf="customStartDate && customEndDate">
                      {{ customStartDate | date: "mediumDate" }} -
                      {{ customEndDate | date: "mediumDate" }}</span>
                    <span fxLayout style="margin: 0 0 0 8px">
                      <mat-date-range-input [rangePicker]="picker" [min]="minDate" [max]="maxDate"
                        style="display: none">
                        <input matStartDate #dateRangeStart [(ngModel)]="customStartDate" />
                        &nbsp;
                        <input matEndDate #dateRangeEnd [(ngModel)]="customEndDate" (dateChange)="
                            dateRangeChange(dateRangeStart, dateRangeEnd)
                          " />
                      </mat-date-range-input>
                      <!-- date picker -->
                      <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle>
                      <mat-date-range-picker class="custom-date-icon" #picker></mat-date-range-picker>
                    </span>
                  </div>
                </mat-option>
              </div>
            </mat-select>
            <div class="date-range-icon">
              <img src="assets/images/calender.svg" />
            </div>
          </mat-form-field>
        </div>

        <mat-form-field appearance="none" class="product-filter" fxFlex="20"
          *ngFor="let item of filterList; let i = index">
          <mat-select [disabled]="item.values.length == 0" placeholder="{{ item.display_name }}"
            [(ngModel)]="selectedProducts[i]" (ngModelChange)="getProductSelection()" name="selectedProducts" multiple>
            <mat-option #mulVal *ngFor="let mulVals of item.values" [value]="mulVals">
              {{ mulVals }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button class="previous-btn" (click)="changetoPrevProduct()">
          <mat-icon i18n-matTooltip matTooltip="Previous" matTooltipPosition="above">west</mat-icon>
        </button>
        <button mat-button class="previous-btn" (click)="changetoNextProduct()">
          <mat-icon i18n-matTooltip matTooltip="Next" matTooltipPosition="above">east</mat-icon>
        </button>
        <!-- reset button -->
        <button mat-button class="reset-btn filled-btn-primary" fxLayout fxLayoutAlign="center center"
          (click)="resetFilters()" i18n i18n-matTooltip matTooltip="Clear search and filters">
          Reset
        </button>
        <div class="view-messages-icon" i18n-matTooltip matTooltip="Comments" matTooltipPosition="above"
          [routerLink]="'/details/comments'" [queryParams]="{
            sub: subscriptionId,
            origin: '/details/review-mode',
            row_id: row_id,
            bucket: bucket
          }">
          <!-- <div class="dot"></div> -->
          <img src="assets/images/message.svg" />
        </div>
        <div>
          <mat-slide-toggle class="text-theme-primary toggle" [checked]="productModeChecked"
            (change)="toggleSwitches($event)" *ngIf="row_id !== undefined" i18n>
            Product Details
          </mat-slide-toggle>
        </div>
      </div>
    </div>
    <div class="chip-wrapper">
      <mat-chip class="search-chip" *ngFor="let item of searchedItems" [selectable]="selectable" [removable]="removable"
        (removed)="removeQuery(item)">
        {{ item }}
        <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
      </mat-chip>
    </div>
  </div>

  <div class="table-wrapper">
    <div class="review-tabel" fxLayout="column">
      <!-- product index -->
      <div *ngIf="currentItem && totalItems && totalItems > 0" class="product-count">
        <span class="current-item">{{ currentItem }}</span>
        <span class="total-items">/ {{ totalItems }} </span>
      </div>

      <mat-toolbar class="review-header" fxLayoutAlign="space-between">
        <div class="head" fxFlex="40" i18n>Product Details</div>
        <div class="head" fxFlex="40" i18n>Suggestions & Edits</div>
        <div class="head" fxFlex="20" i18n>Comments</div>
      </mat-toolbar>
      <!-- no review data -->
      <div *ngIf="dataLoading" class="no-review-data" fxLayoutAlign="center center" fxFlex="100">
        <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
      </div>

      <div fxLayout="column" class="review-viewport" *ngIf="reviewData?.length > 0 && !dataLoading">
        <!-- <app-loadingspinner *ngIf="loadingData && !pageload"></app-loadingspinner> -->
        <mat-card fxLayoutAlign="start space-between" class="review-page-main-card"
          *ngFor="let item of reviewData; let i = index" fxLayoutGap="20px">
          <!-- product details (rightmost section) -->
          <div class="product-details" fxFlex="33" fxLayout="column" fxLayoutGap="10px">
            <!-- showing 2 images with count overlay -->
            <div fxLayout="row" *ngIf="!item.showAllImg || item.showAllImg == false">
              <div class="product-images" *ngFor="let img of item.images | slice: 0:3; let j = index">
                <span *ngIf="j < 2 && img.value">
                  <img src="{{ img.value }}" (click)="openImageSlider([img])" />
                </span>
                <small class="tag active" fxLayoutAlign="center center" *ngIf="img.tag && j != 2">
                  {{ img.tag }}
                </small>
                <span class="overlay" *ngIf="j == 2 && img.value" (click)="item.showAllImg = true">
                  <img src="{{ img?.value }}" />
                  <h3>{{ item.images.length - j }}+</h3>
                </span>
              </div>
            </div>
            <!-- showing all images-->
            <div fxLayout="row" *ngIf="item.showAllImg == true">
              <div class="product-images" *ngFor="let img of item.images">
                <span>
                  <img [src]="img.value" (click)="openImageSlider([img])" />
                  <small class="tag active" fxLayoutAlign="center center" *ngIf="img.tag">
                    {{ img.tag }}
                  </small>
                </span>
              </div>
            </div>
            <!-- titles -->
            <div *ngFor="let title of item.title" class="title">
              <!-- <p class="text-theme-primary" *ngIf="title.type == 'CLICKABLE'">
                {{ title.value }}
              </p> -->

              <p class="product-title text-theme-primary" *ngIf="title.type == 'TITLE'">
                {{ title.value }}
              </p>

              <p class="ref-url" *ngIf="title.data_type == 'ASSET_LINK'">
                <span i18n>References</span>
                <mat-icon class="url-icon text-theme-primary" (click)="openDialogReferenceURL(title.value)">link
                </mat-icon>
              </p>

              <p class="product-description" *ngIf="title.type == 'DESCRIPTION'" style="overflow-x: auto">
                <span>{{
                  title.value.length > 100 ? (title.value | slice: 0:100) : ""
                }}</span>
                <span attr.id="dots-{{ i }}" *ngIf="title.value.length > 100">...</span>
                <span attr.id="more-{{ i }}" style="display: none">
                  {{ title.value | slice: 100:title.value.length }}
                </span>
                <span *ngIf="title.value.length > 100" (click)="viewMore($event, i)" attr.id="moreBtn-{{ i }}"
                  class="view-more text-theme-primary" style="cursor: pointer" i18n>
                  View More</span>
              </p>
            </div>
            <!-- asset list (doc/mp3/mp4/svg) -->
            <div class="file-download" fxLayout="row" *ngIf="item.assests?.length > 0">
              <div fxLayout="column" class="icon-container" *ngFor="let asset of item.assets">
                <a fxLayout href="{{ asset.value }}" target="_blank" rel="noopener noreferrer"
                  *ngIf="asset.value != ''">
                  <img src="../../../assets/images/review-page/DOC.svg" *ngIf="asset.type === 'DOC'"
                    matTooltip="{{ asset.caption }}" [matTooltipPosition]="'above'" />
                  <img src="../../../assets/images/review-page/MP3.svg" *ngIf="asset.type === 'AUDIO'"
                    matTooltip="{{ asset.caption }}" [matTooltipPosition]="'above'" />
                  <img src="../../../assets/images/review-page/MP4.svg" *ngIf="asset.type === 'VIDEO'"
                    matTooltip="{{ asset.caption }}" [matTooltipPosition]="'above'" />
                  <img src="../../../assets/images/review-page/ZIP.svg" *ngIf="asset.type === 'ZIP'"
                    matTooltip="{{ asset.caption }}" [matTooltipPosition]="'above'" />
                </a>
              </div>
            </div>
            <!-- view details link-->
            <span class="product-caption text-theme-primary" [routerLink]="['/details/product-details']" [queryParams]="{
              sub: subscriptionId,
              row_id: item.row_id,
              from: 'review',
              bucket: bucket
            }" i18n>View Details</span>
            <!-- bucket update action -->
            <div class="move_to_frmField" fxLayout="row" *ngIf="moveToBucketList && moveToBucketList.length > 0">
              <mat-form-field appearance="none">
                <mat-select i18n-placeholder placeholder="Move To" [disableOptionCentering]="true">
                  <mat-option *ngFor="let bucket of moveToBucketList" (click)="updateBucket(rowId, bucket.value)"
                    [value]="bucket.value">
                    {{ bucket.display_name  }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>

          <!-- suggestion and edits section-->
          <div class="suggestion-edit-section" fxFlex="33">
            <div *ngIf="item.predictions.length == 0" fxLayoutAlign="center center" fxLayoutGap="5px"
              class="no-review-suggestions">
              <mat-icon fontSet="material-icons-outlined">info</mat-icon>
              <span class="no-pred-found" i18n> No Suggestions available </span>
            </div>
            <!-- suggestions & edit -->
            <div *ngFor="
                let pre of item.showAllSuggestions
                  ? item.predictions
                  : (item.predictions | slice: 0:defSuggestionDisplayCount);
                let j = index
              ">
              <!-- <p class="suggestion-card-label" *ngIf="pre.main_attribute">
                {{ pre.display_name }}
              </p> -->
              <!-- <p class="suggestion-card-label" *ngIf="!pre.main_attribute && j == item.main_attribute_count" i18n>
                ATTRIBUTES
              </p> -->
              <mat-card class="suggestion-edit-card" tabindex="0" [ngClass]="{
                  'active-selection': pre.display_name == item.activeAttribute
                }" (click)="item.activeAttribute = pre.display_name">
                <div fxLayout class="attribute-name">
                  <p>
                    {{ pre.display_name }}
                  </p>
                </div>
                <!-- suggestions & edit value -->
                <div class="existing-section">
                  <p class="attr-existing" i18n>Existing</p>
                  <p class="attr-value" *ngFor="let e of pre.existing_values">
                    {{ e }}
                  </p>
                  <!-- suggestion -->
                  <div *ngIf="pre.suggestion_values.length > 0">
                    <div class="hr-line"></div>
                    <p class="attr-suggestion" i18n>Suggestion</p>
                    <div *ngFor="
                        let suggestion of pre.suggestion_values;
                        let i = index
                      ">
                      <!-- Accept / Reject -->
                      <div fxLayout class="action-btn" fxLayoutGap="10px" fxLayout="row">
                        <img (click)="
                            updateSuggestion(
                              item,
                              pre,
                              item.row_id,
                              suggestion,
                              'accept',
                              true
                            )
                          " src="../../../assets/images/review-page/accept.svg" />
                        <img (click)="
                            updateSuggestion(
                              item,
                              pre,
                              item.row_id,
                              suggestion,
                              'reject',
                              false
                            )
                          " src="../../../assets/images/review-page/reject.svg" />
                      </div>
                      <p (click)="
                          updateSuggestion(
                            item,
                            pre,
                            item.row_id,
                            suggestion,
                            'accept',
                            false
                          )
                        " class="attr-value">
                        {{ suggestion }}
                      </p>
                    </div>
                  </div>
                </div>
              </mat-card>
            </div>
            <button mat-button class="more-suggestions-btn" *ngIf="
                !item.showAllSuggestions &&
                item.predictions.length > defSuggestionDisplayCount
              " (click)="item.showAllSuggestions = true" i18n>
              {{ item.predictions.length - defSuggestionDisplayCount }} More
              Suggestions
              <mat-icon>arrow_drop_down</mat-icon>
            </button>
          </div>

          <!-- comment thread scroll container -->
          <div class="review-comments" fxFlex="30" #scrollContainer fxLayout="column" fxLayoutGap="20px"
            fxLayoutAlign="start center" infiniteScroll [scrollWindow]="false" [infiniteScrollDistance]="1"
            [infiniteScrollUpDistance]="1" [infiniteScrollThrottle]="50" (scrolled)="onCommentThreadScroll()">
            <!-- comment area -->
            <div class="comment-box" fxLayout="row">
              <div fxLayout="column" class="cmt-textarea" fxLayoutAlign="start">
                <!-- -----------   create Editor starts   --------- -->
                <div [ngClass]="{ 'editor-fs': isFullscreen }" #fullScreen>
                  <quill-editor [placeholder]="editorPlaceholder" [styles]="editorStyle" [modules]="modules"
                    [(ngModel)]="content" theme="snow" (onContentChanged)="onContentChange($event, 'create')"
                    (onEditorCreated)="getEditorInstance($event)" customToolbarPosition="bottom">
                    <div quill-editor-toolbar>
                      <span class="ql-formats">
                        <button class="ql-bold" i18n-matTooltip matTooltip="Bold" matTooltipPosition="above"></button>
                        <button class="ql-italic" i18n-matTooltip matTooltip="Italic"
                          matTooltipPosition="above"></button>
                        <button class="ql-underline" i18n-matTooltip matTooltip="Underline"
                          matTooltipPosition="above"></button>
                        <button class="ql-strike" i18n-matTooltip matTooltip="Strikethrough"
                          matTooltipPosition="above"></button>
                        <button class="ql-blockquote" i18n-matTooltip matTooltip="Quote"
                          matTooltipPosition="above"></button>
                        <button class="ql-code" i18n-matTooltip matTooltip="Insert code"
                          matTooltipPosition="above"></button>
                        <select class="ql-color" title="Text colour" i18n-title></select>
                        <button class="ql-header" value="1" i18n-matTooltip matTooltip="Heading 1"
                          matTooltipPosition="above"></button>
                        <button class="ql-header" value="2" i18n-matTooltip matTooltip="Heading 2"
                          matTooltipPosition="above"></button>
                        <button class="ql-list" value="ordered" i18n-matTooltip matTooltip="Numbered list"
                          matTooltipPosition="above"></button>
                        <button class="ql-list" value="bullet" i18n-matTooltip matTooltip="Bullet list"
                          matTooltipPosition="above"></button>
                        <button class="ql-link" i18n-matTooltip matTooltip="Insert link"
                          matTooltipPosition="above"></button>
                        <input style="display: none" type="file" accept="image/*" (change)="insertImage($event)"
                          #fileInput>
                        <button (click)="fileInput.click()" *ngIf="!isImgUpload">
                          <mat-icon style="font-size: 18px;margin: -1px; color:#444444; padding-top: 2px;"
                            i18n-matTooltip matTooltip="Insert image" matTooltipPosition="above">collections</mat-icon>
                        </button>
                        <button *ngIf="isImgUpload">
                          <mat-progress-spinner color="primary" mode="indeterminate" diameter="20">
                          </mat-progress-spinner>
                        </button>
                        <button class="ql-attachment" *ngIf="files.length == 0" i18n-matTooltip
                          matTooltip="Attach a file" matTooltipPosition="above">
                          <label for="inputTag">
                            <mat-icon style="font-size: 22px;margin: -1px; color:#444444 !important;"
                              class="attach-file">attachment</mat-icon>
                            <input id="inputTag" type="file" style="display: none;"
                              (change)="onFileSelected($event, 'create')" />
                          </label>
                        </button>
                        <button (click)="toggleFullscreen(!isFullscreen, 'create')" class="ql-fullscreen">
                          <mat-icon *ngIf="!isFullscreen" style="
                              font-size: 18px;
                              margin: -1px;
                              color: #444444;
                            " i18n-matTooltip matTooltip="Go fullscreen" matTooltipPosition="above">open_in_full
                          </mat-icon>
                          <mat-icon *ngIf="isFullscreen" style="
                              font-size: 18px;
                              margin: -1px;
                              color: #444444;
                            " i18n-matTooltip matTooltip="Close fullscreen" matTooltipPosition="above">close_fullscreen
                          </mat-icon>
                        </button>
                      </span>
                    </div>
                  </quill-editor>
                </div>
                <!-- create editor ends -->
                <!-- create editor actions -->
                <div class="create-comment-footer" fxLayout="row" fxLayoutAlign="space-between center"
                  style="margin-top: 10px">
                  <!-- <mat-icon class="text-theme-primary attach-file">attachment</mat-icon> -->
                  <div class="chip-row">
                    <ng-container *ngIf="files.length > 0">
                      <div class="file-chip" fxLayout="row" fxLayoutAlign="space-between center"
                        [matTooltip]="file.name" matTooltipPosition="above" *ngFor="let file of files; let i = index">
                        <span> {{ file.name | truncate: 5 }} </span>
                        <mat-icon (click)="clear(i)">cancel</mat-icon>
                      </div>
                    </ng-container>
                  </div>
                  <button mat-raised-button class="filled-btn-primary" (click)="handler('create')" [disabled]="
                      selectedUsers.length <= 0 &&
                      (content == '' || content == null) &&
                      files.length <= 0
                    " i18n>
                    Send
                  </button>
                </div>
              </div>
            </div>
            <div class="cmt-profile" *ngFor="let comment of commentThread; let i = index" fxLayoutAlign="center start">
              <!-- show edit/delete on comment hover -->
              <!-- preview comment -->
              <div fxLayout="row" [class.hover-color]="row === i" *ngIf="!comment.editable"
                fxLayoutAlign="space-between start" fxFlex="100" (mouseover)="
            userData.username == comment.created_by_username
              ? (row = i)
              : (row = -1)
          " (mouseleave)="row = -1">
                <!-- comment profile picture -->
                <img src="{{ comment.profile_picture }}" />
                <div fxLayout="column" class="cmt-profile-picture">
                  <!-- comment header -->
                  <div fxLayout="row" class="comment-head" fxLayoutAlign="space-between center">
                    <div fxLayout="column">
                      <span class="username">{{ comment.created_by }}</span>
                      <span class="comment-time">
                        {{ comment.created_at | date: 'y/MM/dd' }}
                      </span>
                    </div>
                    <span style="padding-left: 20px;" fxLayout="row" fxLayoutGap="5px" *ngIf="
                    userData.username == comment.created_by_username && row == i
                ">
                      <img src="../../../assets/images/comments-page/edit.svg"
                        (click)="editComment(comment); comment.editable = true" i18n-matTooltip matTooltip="Edit"
                        matTooltipPosition="above" />
                      <img class="delete-icon" src="../../../assets/images/comments-page/trash.svg"
                        (click)="deleteComment(comment.comment_id, i)" i18n-matTooltip matTooltip="Delete"
                        matTooltipPosition="above" />
                    </span>
                  </div>

                  <!-- comment content -->
                  <div class="comments" fxLayoutAlign="space-between center" fxFlex="100">
                    <div>
                      <div class="comment-text editor-comments-img">
                        <span [innerHTML]="comment.text | innerHTMLstyles"></span>
                      </div>
                      <!-- <span class="comment-text">{{comment.attachment.name}}</span> -->
                      <div fxLayout="row" *ngIf="comment.attachment.name" class="chip-row"
                        [matTooltip]="comment.attachment.name" matTooltipPosition="above">
                        <a [href]="comment.attachment.signed_url">
                          <span class="file-chip">{{comment.attachment.name | truncate:8}}
                          </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- edit comment part -->
              <div class="edit-comment-box" fxLayout="column" *ngIf="comment.editable" style="width: 100%">
                <div fxLayout="row" fxLayoutAlign="space-between start" fxFlex="100">
                  <!-- profile picture -->
                  <img src="{{ comment.profile_picture }}" />
                  <div fxLayout="column" class="cmt-profile-picture">
                    <!-- comment header -->
                    <div fxLayout="row" class="comment-head" fxLayoutAlign="space-between center">
                      <div fxLayout="column">
                        <span class="username">{{ comment.created_by }}</span>
                        <span class="comment-time">
                          {{ comment.created_at | date: 'y/MM/dd' }}
                        </span>
                      </div>
                    </div>
                    <!-- edit comment editor -->
                    <div [ngClass]="{'editor-fs':isEditFullscreen}" #fullScreenEdit style="margin-top: 5px">
                      <quill-editor [placeholder]=editorPlaceholder [styles]="editorStyle" [modules]="editModuleconfig"
                        [(ngModel)]="comment.edited_text" theme="snow"
                        (onContentChanged)="onContentChange($event, 'edit', comment)"
                        (onEditorCreated)="getEditorInstance($event,'edit')" customToolbarPosition="bottom">
                        <div quill-editor-toolbar>
                          <span class="ql-formats">
                            <button class="ql-bold" i18n-matTooltip matTooltip="Bold"
                              matTooltipPosition="above"></button>
                            <button class="ql-italic" i18n-matTooltip matTooltip="Italic"
                              matTooltipPosition="above"></button>
                            <button class="ql-underline" i18n-matTooltip matTooltip="Underline"
                              matTooltipPosition="above"></button>
                            <button class="ql-strike" i18n-matTooltip matTooltip="Strikethrough"
                              matTooltipPosition="above"></button>
                            <button class="ql-blockquote" i18n-matTooltip matTooltip="Quote"
                              matTooltipPosition="above"></button>
                            <button class="ql-code" i18n-matTooltip matTooltip="Insert code"
                              matTooltipPosition="above"></button>
                            <select class="ql-color" i18n-title title="Text colour"></select>
                            <button class="ql-header" value="1" i18n-matTooltip matTooltip="Heading 1"
                              matTooltipPosition="above"></button>
                            <button class="ql-header" value="2" i18n-matTooltip matTooltip="Heading 2"
                              matTooltipPosition="above"></button>
                            <button class="ql-list" value="ordered" i18n-matTooltip matTooltip="Numbered list"
                              matTooltipPosition="above"></button>
                            <button class="ql-list" value="bullet" i18n-matTooltip matTooltip="Bullet list"
                              matTooltipPosition="above"></button>
                            <button class="ql-link" i18n-matTooltip matTooltip="Insert link"
                              matTooltipPosition="above"></button>
                            <!-- Embed Image -->
                            <input style="display: none" type="file" accept="image/*"
                              (change)="insertImage($event, 'edit')" #fileInputEdit>
                            <button (click)="fileInputEdit.click()" *ngIf="!isImgUploadEdit">
                              <mat-icon style="font-size: 18px;margin: -1px; color:#444444; padding-top: 2px;"
                                i18n-matTooltip matTooltip="Insert image" matTooltipPosition="above">collections
                              </mat-icon>
                            </button>
                            <button *ngIf="isImgUploadEdit">
                              <mat-progress-spinner color="primary" mode="indeterminate" diameter="20">
                              </mat-progress-spinner>
                            </button>
                            <!-- File Attachment -->
                            <button class="ql-attachment" i18n-matTooltip
                              *ngIf="isAllowAttachment && filesEdit?.length == 0" matTooltip="Attach a file"
                              matTooltipPosition="above">
                              <label for="inputTag2">
                                <mat-icon style="font-size: 22px;margin: -1px; color:#444444 !important;"
                                  class="attach-file">attachment</mat-icon>
                                <input id="inputTag2" type="file" style="display: none;"
                                  (change)="onFileSelected($event, 'edit')" />
                              </label>
                            </button>
                            <!-- Full screen -->
                            <button (click)="toggleFullscreen(!isEditFullscreen, 'edit')" class="ql-fullscreen">
                              <mat-icon *ngIf="!isEditFullscreen" style="font-size: 18px;margin: -1px; color:#444444"
                                i18n-matTooltip matTooltip="Go fullscreen" matTooltipPosition="above">open_in_full
                              </mat-icon>
                              <mat-icon *ngIf="isEditFullscreen" style="font-size: 18px;margin: -1px; color:#444444"
                                i18n-matTooltip matTooltip="Close fullscreen" matTooltipPosition="above">
                                close_fullscreen
                              </mat-icon>
                            </button>
                          </span>
                        </div>
                      </quill-editor>
                    </div>
                  </div>
                </div>
                <!-- Update/Cancel Action Buttons and File Attachments-->
                <div class="edit-action-footer" fxLayout="row" fxLayoutAlign="space-between center">
                  <div>
                    <!-- incoming attachments -->
                    <div *ngIf="comment['attachment'].hasOwnProperty('name') && !isEditFileRemoved"
                      class="edit-files-chip-row">
                      <span class="file-chip" fxLayout="row" fxLayoutAlign="space-between center">
                        <span [matTooltip]="comment.attachment.name"
                          matTooltipPosition="above">{{comment.attachment.name | truncate:8 }}</span>
                        <mat-icon (click)="clearEditAttachment(comment, i)">cancel</mat-icon>
                      </span>
                    </div>
                    <!-- new attachments -->
                    <div *ngIf="editUploadcheck" class="chip-row">
                      <span class="file-chip" *ngFor="let file of filesEdit let i = index" fxLayout="row"
                        fxLayoutAlign="space-between center">
                        <span>{{file.name | truncate: 8 }}</span>
                        <mat-icon (click)="clearEditAttachment(comment,i)">cancel</mat-icon>
                      </span>
                    </div>
                  </div>
                  <!-- comment edit actions -->
                  <div class="actions" fxLayout="row" fxLayoutGap="10px">
                    <button mat-button (click)="onCancelEdit(comment)">
                      <span i18n>Cancel</span>
                    </button>
                    <button mat-button color="primary" (click)="handler('edit', comment)"
                      [disabled]="!isCommentChanged ? true : (!comment.edited_text || comment.edited_text == null ) && isAllowAttachment">
                      <span i18n>Update</span>
                    </button>
                  </div>
                </div>
              </div>
              <div class="loading-comments" fxLayoutAlign="center center"
                *ngIf="commentsLoading && commentThread.length == 0" fxFlex="100">
                <mat-spinner fxLayoutAlign="center center" diameter="40" strokeWidth="3"></mat-spinner>
              </div>
            </div>

            <!-- no comments -->
            <div class="loading-comments" fxLayoutAlign="center center"
              *ngIf="commentsLoading && commentThread.length == 0" fxFlex="100">
              <mat-spinner fxLayoutAlign="center center" diameter="40" strokeWidth="3"></mat-spinner>
            </div>
          </div>
        </mat-card>
      </div>
      <!-- no data section -->
      <div class="no-data" *ngIf="reviewData?.length == 0 && !dataLoading" fxLayout="row" fxLayoutGap="10px">
        <mat-icon>info</mat-icon>
        <span i18n>Nothing to display</span>
      </div>
    </div>
  </div>
</div>
