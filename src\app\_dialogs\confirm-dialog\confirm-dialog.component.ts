import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-confirm-delete',
  templateUrl: './confirm-dialog.component.html',
  styleUrls: ['./confirm-dialog.component.scss']
})
export class ConfirmDialog implements OnInit {
  response;
  status;
  batch_id;
  content;
  primaryBtn;
  secondaryBtn;


  constructor(
    @Inject(MAT_DIALOG_DATA) private data: any,
    public discardDialogRef: MatDialogRef<ConfirmDialog, any>
  ) { }


  ngOnInit(): void {
    this.response = this.data;
    this.primaryBtn = this.response.primarybtnName;
    this.secondaryBtn = this.response.secondaryBtnName;
    this.batch_id = this.response.batch_id;
    this.status = this.response.status;
    this.content = this.response.content;
  }


  triggerAction = (status) => {
    this.data.trigger(status);
    this.discardDialogRef.close(true)
  }

}
