@import "../../../styles/variables";
.product-filter {
  width: auto;
  height: 40px;
  border: 1px solid #c1c4d6;
  box-sizing: border-box;
  border-radius: 4px;
  flex-grow: 1;

  .mat-select {
    width: 92%;
    padding-left: 10px;
    position: absolute;
    margin-top: -10px;
    .mat-select-placeholder {
      color: #3b3e48;
    }
    .mat-select-arrow {
      color: #3b3e48;
    }
  }
}
.batch-chip {
  font-family: $site-font;
  font-style: normal;
  font-weight: normal;
  font-size: 10px;
  line-height: 15px;
  width: fit-content;
  padding-left: 5px;
  padding-right: 5px;
  color: $theme-white;
  box-sizing: border-box;
  border-radius: 40px;
}
.search-chip {
  margin: 15px 10px;
}

.view-messages-icon {
  color: #3b3e48;
  cursor: pointer;
  display: flex;
  height: 40px;
  align-items: center;
}