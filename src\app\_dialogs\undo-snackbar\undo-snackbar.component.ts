import { DataSource } from '@angular/cdk/collections';
import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit, Inject } from '@angular/core';
import {
  MatSnackBarRef,
  MAT_SNACK_BAR_DATA,
  MatSnackBar,
} from '@angular/material/snack-bar';
import { UndoService } from '../../services/undo.service';
import { SnackbarService } from '../../services/snackbar.service';

@Component({
  selector: 'app-undo-snackbar',
  templateUrl: './undo-snackbar.component.html',
  styleUrls: ['./undo-snackbar.component.scss'],
})
export class UndoSnackbarComponent implements OnInit {
  response: any;
  sub_id: any;
  row_id: any;
  loading: boolean;

  constructor(
    public snackBarRef: MatSnackBarRef<UndoSnackbarComponent>,
    @Inject(MAT_SNACK_BAR_DATA)
    public data: any,
    private undoService: UndoService,
    private matSnackbar: MatSnackBar,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit() {
    this.response = this.data.response;
    this.sub_id = this.data.module;
    this.row_id = this.data.Id;
    // console.log(this.sub_id, this.row_id, this.response)
  }

  undoCall = () => {
    this.loading = true;
    this.response = $localize`PROCESSING` + '..';
    this.undoService.undoFunction(this.sub_id, this.row_id).subscribe({
      next: (resp) => {
        this.loading = false;
        this.response = resp.detail;
        this.data.onUndo();
        this.snackbarService.openSnackBar(resp.detail, 'OK');
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.loading = false;
        this.response = this.data.response;
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
  };
}
