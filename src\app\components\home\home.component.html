<div class="wrapper" fxFlex="100" fxLayout="column">
  <div class="filter-container">
    <div class="filter-head" fxLayoutAlign="space-between start">
      <div fxLayout="row" fxLayoutGap="10px" class="search-container">
        <mat-form-field fxFlex="70" fxFlex.gt-md="90" appearance="none" class="search-filter">
          <input id="search" matInput i18n-placeholder placeholder="Search by Batch Id, Name or Description" #searchVal
            name="searchVal" [(ngModel)]="search" (keydown.enter)="getSearchValue(search)" />
          <mat-icon matPrefix class="search-icon" (click)="getSearchValue(search)">search</mat-icon>
          <mat-icon matSuffix class="remove-icon" (click)="resetSearch()" *ngIf="search">close</mat-icon>
        </mat-form-field>
      </div>

      <div class="btn-container" fxLayout="row" fxLayoutGap="10px">
        <!-- search by tag name -->
        <mat-form-field appearance="none" class="tag-filter" *ngIf="labelList?.length > 0">
          <mat-select (openedChange)="openedChange($event)" placeholder="Filter by tags" [formControl]="selectedTagFormControl" multiple>
            <mat-select-trigger>
              {{selectedTagFormControl.value ? selectedTagFormControl.value[0]?.name : ''}}
              <span *ngIf="selectedTagFormControl.value?.length > 1" class="additional-selection">
                (+{{selectedTagFormControl.value.length - 1}} {{selectedTagFormControl.value?.length === 2 ? 'other' : 'others'}})
              </span>
            </mat-select-trigger>
            <div class="select-container">
                <mat-optgroup >
              <mat-form-field style="width:100%;">
                <input #searchTextBoxRef autocomplete="off" placeholder="Search" aria-label="Search" matInput [formControl]="tagQueryCtrl">
                <button [disableRipple]="true" *ngIf="search.value" matSuffix mat-icon-button aria-label="Clear" (click)="clearSearch($event)">
                <mat-icon >close</mat-icon>
              </button>
                    </mat-form-field>
                </mat-optgroup>
                <mat-optgroup *ngIf="(filteredTagOptions | async).length == 0">
                  <div>No results found!</div>
                </mat-optgroup>
                <mat-option (onSelectionChange)="tagSelectionChange($event)" *ngFor="let option of filteredTagOptions | async" [value]="option">
                  {{option.name}}
                </mat-option>
            </div>
          </mat-select>
        </mat-form-field>

        <!-- date filter -->
        <mat-form-field appearance="none" class="date-range-filter">
          <mat-select [(value)]="selected" (selectionChange)="getDataForDate($event.value)">
            <div *ngFor="let option of datePickerOptions">
              <!-- Recent -->
              <mat-option *ngIf="option.value === 'recent'" value="recent">
                <div fxLayout="column" class="range-category" *ngIf="option.value === 'recent'" fxLayout="row">
                  <span i18n> Recent</span> &nbsp;
                  <span class="date-range"> </span>
                </div>
              </mat-option>
              <!-- Last Week / Month / Quarter -->
              <mat-option [value]="option.value" *ngIf="
                  option.value !== 'recent' && option.value !== 'custom_range'
                ">
                <div fxLayout="column" class="range-category">
                  <ng-container>{{ option.display }}</ng-container>
                  <!-- <span i18n="@@roleKey">{option.display, select, role {role}}</span> -->
                  <span class="date-range">
                    {{ option.start_date | date : "mediumDate" }} -
                    {{ currentDate | date : "mediumDate" }}</span>
                </div>
              </mat-option>
              <!-- Custom range  -->
              <mat-option *ngIf="option.value === 'custom_range'" value="custom_range" (click)="picker.open()"
                style="margin: 6px 0 10px 0">
                <div fxLayout="column" >
                  <div fxLayout="row" class="range-category" fxLayoutAlign="start center">
                      <span i18n>Custom Range </span>
                      <mat-date-range-input [rangePicker]="picker" [min]="minDate" [max]="maxDate"
                          style="display: none">
                          <input type="hidden" matStartDate #dateRangeStart [(ngModel)]="customStartDate" />
                          &nbsp;
                          <input type="hidden" matEndDate #dateRangeEnd [(ngModel)]="customEndDate" (dateChange)="
                              dateRangeChange(dateRangeStart, dateRangeEnd)
                          " />
                       </mat-date-range-input>
                       <span fxLayout style="margin: 0 0 0 8px">
                  
                          <!-- <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle> -->
                          <mat-date-range-picker class="custom-date-icon" #picker></mat-date-range-picker>
                      </span>
                  </div>
                  <span class="date-range" *ngIf="customStartDate && customEndDate">
                  {{ customStartDate | date: "mediumDate" }} -
                  {{ customEndDate | date: "mediumDate" }}</span>
                  
              </div>
              </mat-option>
            </div>
          </mat-select>
          <div class="date-range-icon">
            <img src="assets/images/calender.svg" />
          </div>
        </mat-form-field>
        <!-- reset btn -->
        <button mat-button class="reset-btn" style="color: gray" i18n fxLayout fxLayoutAlign="center center"
          (click)="reset()">
          Reset
        </button>
        <!-- export batches -->
        <button mat-stroked-button class="upload-btn stroked-btn-primary" (click)="exportAllBatches()" i18n i18n-matTooltip
           matTooltipPosition="above">
          <mat-icon class="upload-btn-icon">ios_share</mat-icon> 
           Export All Batches
        </button>
        <!-- upload new batch -->
        <button  mat-raised-button *ngIf="permissionsObject['add_batch']"
          class="upload-btn filled-btn-primary" (click)="toggleSideNav('uploadBatch', '')" i18n i18n-matTooltip
          matTooltip="Upload batch of SKUs to be enriched." matTooltipPosition="above">
          <mat-icon class="upload-btn-icon">add</mat-icon> Upload New Batch
        </button>
        
        <div class="view-messages-icon" i18n-matTooltip matTooltip="View comments" matTooltipPosition="above"
          [routerLink]="'/details/comments'" [queryParams]="{ sub: SubscriptionID, origin: '/home' }">
          <!-- <div class="dot"></div> -->
          <img src="assets/images/message.svg" />
        </div>
      </div>

      
    </div>
  </div>

  <div class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px" *ngIf="!dataLoading">
    <mat-tab-group #tabGroup (selectedTabChange)="tabChanged($event)" [selectedIndex]="selectedIndex">
      <mat-tab [label]="tabHeader" *ngFor="let tabHeader of tabList">
        <ng-template mat-tab-label>
          <span matTooltip="{{ tabHeader.desc }}" matTooltipPosition="above">{{ tabHeader.display }} </span>&nbsp;
          <span class="batch-count" fxLayoutAlign="center center">
            {{ headercount && headercount[tabHeader.value] }}</span>
        </ng-template>
        <div class="mat-elevation-z8" class="table-section">
          <!-- table -->
          <table mat-table [dataSource]="homeDataSource">
            <!-- Position Column -->
            <ng-container matColumnDef="{{ item.slug }}" *ngFor="let item of productHeaderData">
              <th mat-header-cell *matHeaderCellDef  [matTooltip]="item.slug == 'SC Batch ID' ? 'Supplier Connector Batch ID':''" matTooltipPosition="above">
                <span>
                  {{ item.display }}
                </span>
              </th>
              <td mat-cell *matCellDef="let element">
                <div fxLayout="column" *ngIf="item.slug == 'Batch ID'" >
                  <div fxLayout="row" fxLayoutAlign="start center">
                    <a [ngClass]="{
                        'disable-link': !permissionsObject['view_batch']
                      }" class="text-theme-primary" [routerLink]="['/products']" [queryParams]="{
                        batch_id: element.batch_id,
                        sub: SubscriptionID
                      }">
                      {{ element.batch_id }}
                    </a>
                    <span>
                      <mat-icon class="copy-icon" style="margin-top: 0; position: relative;
    top: 2px;" [cdkCopyToClipboard]="element.batch_id" i18n-matTooltip
                        matTooltip="Copy text" matTooltipPosition="above" (cdkCopyToClipboardCopied)="showSnackbar()">
                        content_copy</mat-icon>
                    </span>
                  </div>
                  <span class="batch-date" style="margin-top: 0" *ngIf="element.action">
                    <span matTooltip="{{element.action}}" matTooltipPosition="above">Task:
                      {{ element.action | truncate : 10}}
                    </span>
                  </span>
                  <span class="batch-split-group-id" matTooltip="Group ID: {{element.batch_split_group_id}}" matTooltipPosition="above">{{element.batch_split_group_id}}
                  </span>
                </div>

                <div fxLayout="column" *ngIf="item.slug == 'Name & Description'">
                  <div fxLayout="row" fxLayoutAlign="start center">
                    <div fxLayout="column">
                      <span class="batch-name" matTooltip="{{ element.name }}" matTooltipPosition="above">
                        {{ element.name | truncate : 10 }}
                      </span>
                      <span class="description" *ngIf="element.description; else noData"
                        matTooltip="{{ element.description }}" matTooltipPosition="above">
                        {{ element.description | truncate : 15 }}
                      </span>
                    </div>
                    <div class="download-icon" i18n-matTooltip matTooltip="Download input file"
                      matTooltipPosition="above">
                      <!-- <a [href]="" download target="_blank"> -->
                      <mat-icon class="download-batch active"
                        (click)="downloadInputFile(SubscriptionID, element.batch_id)">arrow_downward</mat-icon>
                      <!-- </a> -->
                    </div>
                  </div>
                </div>
                <div class="tags-container" fxLayout="row" *ngIf="item.slug == 'Tags'">
                  <mat-chip-list>
                    <mat-chip class="label-chip" *ngFor="let label of element.labels" [matTooltip]="label.name"
                      matTooltipPosition="above" [ngStyle]="{ 'background-color': label.bg_colour_code, 'color': label.text_colour_code, 'border': '1px solid #e4e4e4' }">
                      {{ label.name }}
                      <mat-icon class="remove-label-chip" (click)="removeLabel(element.batch_id, label.id, '')">close
                      </mat-icon>
                    </mat-chip>
                    <button class="add-label-btn stroked-btn-primary" fxLayoutAlign="center center"
                      (click)="toggleSideNav('addTag', element.batch_id)">
                      <mat-icon class="add-label-icon" matPrefix>add</mat-icon>
                    </button>
                  </mat-chip-list>
                  <!-- <div
                    *ngFor="let item of element[item]"
                    class="label-chip"
                    fxLayout="row"
                    fxLayoutAlign="start center"
                    [ngStyle]="{ 'background-color': '#F5CEBE' }"
                  >
                    <span>{{ item }}</span>
                  </div> -->
                </div>
                <!--  -->
                <div *ngIf="item.slug == 'Created On'" fxLayout="row" class="batch-date">
                  <span *ngIf="element.created_at; else noData">
                    {{ element.created_at | date : "y/MM/dd" }}
                  </span>
                  <mat-icon (click)="
                      toggleSideNav('batchLog', '');
                      getLog('batch', element.batch_id)
                    " class="batch-info">info_outline</mat-icon>
                </div>
                <div fxLayout="row" class="eta-div" *ngIf="item.slug == 'ETA'" fxLayoutAlign="start center">
                  <mat-form-field style="width: 0" class="mat-datetime-picker">
                    <input matInput [ngxMatDatetimePicker]="picker"
                      (dateChange)="modifyEta(element.batch_id, $event.value)" [(ngModel)]="element.eta"
                      [min]="today" />
                    <mat-datepicker-toggle matSuffix [for]="picker"
                      [matTooltip]="element.eta ? modifyEtaMsg : addEtaMsg" matTooltipPosition="above">
                    </mat-datepicker-toggle>
                    <ngx-mat-datetime-picker [enableMeridian]="true" #picker></ngx-mat-datetime-picker>
                  </mat-form-field>
                  <span class="eta-date" *ngIf="element.eta; else noData"
                    matTooltip=" {{ element.eta | date : 'y/MM/dd HH:mm' }} ">
                    {{ element.eta | date : "y/MM/dd HH:mm" }}
                  </span>
                </div>
                <!-- element.reference_batch_id -->
                <div fxLayout="column" *ngIf="item.slug == 'References'" style="max-width: 200px;
                overflow-wrap: break-word;" fxLayout="column" fxLayoutGap="4px" fxLayoutAlign="start start">
                  <div *ngIf="element.reference_batch_id && element.reference_batch_id .length > 0; else noData">
                    <ul style="padding-left: 16px; margin-top: 0; margin-bottom: 0; max-width: 200px">
                    <li *ngFor="let el of element.reference_batch_id">
                      <span>{{el}}</span>
                      <mat-icon style="margin-top: 0; position: relative; top: 2px;" class="copy-icon" [cdkCopyToClipboard]="el" i18n-matTooltip
                        matTooltip="Copy Reference" matTooltipPosition="above" (cdkCopyToClipboardCopied)="showSnackbar()">
                        content_copy</mat-icon>
                    </li>
                    </ul>
                  </div>
                </div>
                <div fxLayout="column" *ngIf="item.slug == 'SC Batch ID'">
                  <span *ngIf="element.supplier_connector_batch_id">
                    {{element.supplier_connector_batch_id}}
                  </span>
                </div>
                <div fxLayout="column" *ngIf="item.slug == 'Accepted'" class="accepted-column">
                    <span class="accepted-rows" *ngIf="element.accepted.toString() == 0 || element.accepted > 0">
                      {{ element.accepted }} 
                    </span>
                     <span *ngIf="element.total_rows.toString() == 0 || element.total_rows > 0" class="total-rows">/{{ element.total_rows }}</span>

                  <!-- <span class="sub-text">({{ element.accepted_percent }}%)</span> -->
                </div>
         
                <div fxLayout="column" *ngIf="item.slug == 'Actions'" style="padding-right: 20px">
                  <div *ngIf="element.status == 'In_Progress'" class="progress-wrapper" fxLayout="row"
                    fxLayoutGap="5px">
                    <mat-progress-bar mode="determinate" [value]="element.progress_percent" fxFlex="90">
                    </mat-progress-bar>
                    <p class="progress-bar-value" fxFlex="10">
                      {{ element.progress_percent }}%
                    </p>
                  </div>

                  <div style="width: fit-content" fxLayoutAlign="center center" *ngIf="
                      element.status == 'In_Queue' &&
                      permissionsObject['cancel_batch']
                    " fxLayoutGap="5px">
                    <img style="cursor: pointer" src="assets/images/home-page/cancel-icon.svg" i18n-matTooltip
                      matTooltip="Cancel" matTooltipPosition="above" class="svg-icon"
                      (click)="action(element.batch_id, 'CANCELLED')" />
                    <!-- delete -->
                    <img style="cursor: pointer" src="assets/images/home-page/delete-icon.svg" i18n-matTooltip
                      matTooltip="Delete" matTooltipPosition="above" class="svg-icon"
                      (click)="action(element.batch_id)" />
                  </div>
                  <p *ngIf="
                      element.status == 'Cancelled' &&
                      permissionsObject['cancel_batch']
                    " fxLayoutAlign="center center" class="batch-cancelled">
                    <img style="cursor: pointer" src="assets/images/home-page/delete-icon.svg" i18n-matTooltip
                      matTooltip="Delete" matTooltipPosition="above" class="svg-icon"
                      (click)="action(element.batch_id, 'From_cancelled')" />
                  </p>

                  <div fxLayout="column" class="action-btn">
                    <button *ngIf="
                        element.status == 'Processed' &&
                        permissionsObject['approve_batch']
                      " class="filled-btn-primary" fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                      (click)="openApproveBatchDialog(element.batch_id)" i18n-matTooltip matTooltip="Approve the batch"
                      matTooltipPosition="above" i18n>
                      Approve
                    </button>
                    <!-- <button *ngIf="element.status == 'In_Progress'" class="filled-btn-primary"
                      fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                      (click)="updatebatchStatus(element.batch_id, 'CANCELLED')">
                      Cancel
                    </button> -->
                    <!-- <button *ngIf="element.status == 'Approved'" class="stroked-btn-primary"
                      fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                      (click)="generateOutputFile(element.batch_id)">
                      Generate Output
                    </button> -->

                    <!-- <button
                    matTooltipPosition="above"
                    [matTooltip]="element.fileGenerationInProgress == true || (element.download?.in_progress === true && element.download?.percent_completed) ?
                                  element.download?.message +'('+  element.download?.percent_completed    +'%)'  : element.fileGenerationInProgress === true || (element.download?.in_progress === true && !element.download?.percent_completed) ?
                                  element.download?.message :'Download output file'"
                      *ngIf="(element.status == 'Approved' || element.status == 'Processed') && permissionsObject['download_batch']"
                      class="batch-download" fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                      (click)="
                          downloadOutputFile(element.last_edited_at, element.last_generated_at, element.batch_id, element.download); element.fileGenerationInProgress = true;
                        ">
                      Download
                    </button> -->
                    <div>
                      <button *ngIf="
                          (element.status == 'Approved' ||
                            element.status == 'Processed') &&
                          permissionsObject['download_batch']
                        " class="batch-download filled-btn-without-border" fxLayoutAlign="center center" mat-button
                        fxFlexAlign="center" [matMenuTriggerFor]="module_status" #t="matMenuTrigger" mat-stroked-button
                        fxLayout="row" fxLayoutAlign="space-between center" i18n-matTooltip
                        matTooltip="Download enriched data" matTooltipPosition="above" i18n>
                        Download
                        <img [src]="
                            t.menuOpen
                              ? '../../assets/images/landing-page/up-arrow-white.svg'
                              : '../../assets/images/landing-page/down-arrow-white.svg'
                          " />
                      </button>
                      <mat-menu #module_status="matMenu" style="min-height: auto;">
                        <button mat-menu-item fxLayout="row" fxLayoutGap="5px" matTooltipPosition="above" [matTooltip]="
                            element.fileGenerationInProgress == true ||
                            (element.download?.in_progress === true &&
                              element.download?.percent_completed !== null)
                              ? element.download?.message + '(' + element.download?.percent_completed + '%)'
                              : element.fileGenerationInProgress === true ||
                                (element.download?.in_progress === true &&
                                  !element.download?.percent_completed)
                              ? element.download?.message
                              : downloadOutputFileText
                          " (click)="
                          downloadBatchFiles(element.batch_id, 'ALL');
                            element.fileGenerationInProgress = true
                          ">
                          <span i18n>Output File</span>
                        </button>
                        <button *ngIf="element.asset_download !== null" mat-menu-item fxLayout="row" fxLayoutGap="5px"
                          (click)="downloadAsset(element.asset_download)">
                          <span i18n>Assets</span>
                        </button>
                        <button *ngIf="element.insufficient_rows > 0" mat-menu-item fxLayout="row" fxLayoutGap="5px"
                          matTooltipPosition="above" [matTooltip]="
                            (element.insufficient_download?.in_progress === true &&
                              element.insufficient_download?.percent_completed !== null)
                              ? element.insufficient_download?.message + '(' + element.insufficient_download?.percent_completed + '%)'
                              : (element.insufficient_download?.in_progress === true &&
                                  !element.insufficient_download?.percent_completed)
                              ? element.insufficient_download?.message
                              : downloadInsufficientFileSKUText
                          " (click)="downloadBatchFiles(element.batch_id, 'INSUFFICIENT_DATA')">
                          <span i18n>Insufficient Data SKUs</span>
                        </button>
                        <!-- duplicate rows -->
                        <button *ngIf="element.duplicate_rows > 0" mat-menu-item fxLayout="row" fxLayoutGap="5px"
                          matTooltipPosition="above" [matTooltip]="
                            (element.duplicate_download?.in_progress === true &&
                              element.duplicate_download?.percent_completed !== null)
                              ? element.duplicate_download?.message + '(' + element.duplicate_download?.percent_completed + '%)'
                              : (element.duplicate_download?.in_progress === true &&
                                  !element.duplicate_download?.percent_completed)
                              ? element.duplicate_download?.message
                              : downloadDuplicateFileSKUText
                          " (click)="downloadBatchFiles(element.batch_id, 'DUPLICATE')">
                          <span i18n>Duplicate SKUs</span>
                        </button>
                        <!-- other rows -->
                        <button *ngIf="element.other_rows > 0" mat-menu-item fxLayout="row" fxLayoutGap="5px"
                          matTooltipPosition="above" [matTooltip]="
                            (element.other_download?.in_progress === true &&
                              element.other_download?.percent_completed !== null)
                              ? element.other_download?.message + '(' + element.other_download?.percent_completed + '%)'
                              : (element.other_download?.in_progress === true &&
                                  !element.other_download?.percent_completed)
                              ? element.other_download?.message
                              : downloadOtherFileSKUText
                          " (click)="downloadBatchFiles(element.batch_id, 'OTHER')">
                          <span i18n>Other SKUs</span>
                        </button>
                      </mat-menu>
                    </div>
                  </div>
                </div>
                <div fxLayout="column" *ngIf="item.slug == 'Comments'">
                  <div class="comments-icon">
                    <div class="dot-comments" *ngIf="element.has_comments"></div>
                    <img [routerLink]="['/details/comments']" [queryParams]="{
                        origin: '/home',
                        batch_id: element.batch_id,
                        sub: SubscriptionID
                      }" src="assets/images/message.svg" />
                  </div>
                </div>

                <!-- <div fxLayout="column" *ngIf="item.slug == 'More'" style="width: 50px;">
                  <div>
                    <button disableRipple mat-icon-button [matMenuTriggerFor]="menu"
                      [ngClass]=" moreIconVisibility(element?.reference_batch_id) ? 'more-icon': 'disable-more-icon'">
                      <mat-icon>more_vert</mat-icon>
                    </button>


                    <mat-menu #menu="matMenu" yPosition="below" xPosition="before">
                      <div class="ref-header" fxLayout="row" fxLayoutAlign="space-between center">
                        <span (click)="$event.stopPropagation()">References
                          <mat-icon (click)="$event.stopPropagation()" class="copy-icon-references"
                            [cdkCopyToClipboard]="
                              element?.reference_batch_id
                                ?.join('\n')
                                ?.toString()
                            " i18n-matTooltip matTooltip="Copy reference" matTooltipPosition="above"
                            (cdkCopyToClipboardCopied)="showSnackbar()">
                            content_copy</mat-icon>
                        </span>

                        <button disableRipple mat-icon-button aria-label="close dialog">
                          <mat-icon class="close-ref-icon">close</mat-icon>
                        </button>
                      </div>

                      <button (click)="$event.stopPropagation()" [disableRipple]="true" mat-menu-item *ngFor="
                          let item of element?.reference_batch_id;
                          let first = first;

                        ">
                        <span matTooltipClass="no-truncate-tooltip" [matTooltip]="item"
                          matTooltipPosition="above">{{ item  | truncate : 15}}</span>
                      </button>
                    </mat-menu>
                  </div>
                </div> -->
                <ng-template #noData></ng-template>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns" ></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
          <!-- progress spinner -->
          <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="tableDataLoading">
            <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
          </div>
          <!-- no data section -->
          <div class="no-data" *ngIf="batchList?.length == 0 && !tableDataLoading" fxLayout="row" fxLayoutGap="10px">
            <mat-icon>info</mat-icon>
            <span *ngIf="tabHeader.value == 'in_queue'" i18n>No batch in queue.
            </span>
            <span *ngIf="tabHeader.value == 'in_progress'" i18n>No batch in progress.
            </span>
            <span *ngIf="tabHeader.value == 'processed'" i18n>Processing has not started.
            </span>
            <span *ngIf="tabHeader.value == 'approved'" i18n>No batch has been approved yet.
            </span>
            <span *ngIf="tabHeader.value == 'cancelled'" i18n>No batch has been cancelled.
            </span>
          </div>
          <mat-paginator [length]="totalItems" [pageSize]="size" [pageIndex]="page - 1" *ngIf="batchList?.length > 0"
            [pageSizeOptions]="[10, 20, 50, 100]" (page)="onPaginateChange($event)" showFirstLastButtons>
          </mat-paginator>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>

  <div class="data-loading-spinner" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="90" strokeWidth="3"></mat-spinner>
  </div>
</div>

<ng-template #panelContent>
  <mat-progress-bar mode="determinate" class="file-upload-progress" [value]="uploadProgress"
    matTooltip="{{ uploadProgress | number : '1.0-0' }}%" matTooltipPosition="above"
    *ngIf="fileUploadStatus === 'Uploading'">
  </mat-progress-bar>
  <div *ngIf="uploadBatch" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between center">
      <p class="panel-header" i18n>Upload New Batch</p>
      <img class="batch-stop-icon" [ngClass]="{ 'disable-close': fileUploadStatus === 'Uploading' }"
        src="assets/images/home-page/close.svg" (click)="closeSidepanel()" />
    </div>
    <!-- drop box -->
    <ngx-dropzone (change)="onSelect($event)" [disabled]="fileUploadStatus == 'Uploading'" [multiple]="false"
      accept="text/csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, application/wps-office.xlsx, application/zip, application/pdf, .xls, .xlsx, .csv, .zip, .txt"
      style="text-align: center">
      <ngx-dropzone-label fxLayout="column" fxLayoutAlign="center center">
        <mat-icon fxLayoutAlign="space-between center">folder_open</mat-icon>
        <span i18n>Drag and Drop your files here</span>
      </ngx-dropzone-label>
      <ng-container>
        <ngx-dropzone-preview *ngFor="let f of files" [removable]="true" (removed)="onRemove(f)">
          <ngx-dropzone-label [matTooltip]="f.name" matTooltipPosition="above">{{ f.name | truncate : 25 }}
          </ngx-dropzone-label>
        </ngx-dropzone-preview>
      </ng-container>
    </ngx-dropzone>
    <!-- <p class="dropzone-info" i18n>
      Upload the data similar to the format specified in the
      <a class="sample-docs text-theme-primary">
        <ng-container>Sample Document</ng-container>
        <span>
          <img class="download-sample-doc" src="../../../assets/images/home-page/download.svg" class="svg-icon" />
        </span>
      </a>
    </p> -->
    <!-- description box -->
    <form class="upload-form" [formGroup]="uploadBatchForm" (ngSubmit)="upload()">
      <p i18n>Batch Name*</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <input matInput i18n-placeholder placeholder="Eg. Batch_104.csv" formControlName="name"
          [errorStateMatcher]="matcher" />
        <mat-error *ngIf="uploadBatchForm.controls['name'].hasError('required')" i18n>
          Batch name is required
        </mat-error>
        <mat-error *ngIf="uploadBatchForm.controls['name'].hasError('maxlength')" i18n>
          Batch name should not exceed 100 characters
        </mat-error>
        <mat-error *ngIf="uploadBatchForm.controls['name'].hasError('pattern') || uploadBatchForm.controls['name'].hasError('whitespace')">Please enter a valid batch name </mat-error>
      </mat-form-field>
      <!-- sc batch id -->
      <div fxLayout="row" fxLayoutGap="5px" fxLayoutAlign="start center">
        <span i18n style="font-size: 14px; font-weight: 600; color: #222329">SC Batch Id </span>
        <mat-icon style="height: 20px; width: 20px; font-size: 20px;" matTooltip="Supplier Connector Batch ID" matTooltipPosition="above" class="batch-info">info_outline</mat-icon>
      </div>
        <mat-form-field class="example-full-width" appearance="outline">
          <input matInput i18n-placeholder placeholder="Eg. E43GyP4" formControlName="sc_batch_id"
            [errorStateMatcher]="matcher" />
          <mat-error *ngIf="uploadBatchForm.controls['sc_batch_id'].hasError('maxlength')" i18n>
            SC Batch Id should not exceed 15 characters
          </mat-error>
          <mat-error *ngIf="uploadBatchForm.controls['sc_batch_id'].hasError('pattern') || uploadBatchForm.controls['sc_batch_id'].hasError('whitespace')">Please enter a valid SC batch id </mat-error>
        </mat-form-field>
      <!-- input format template IDs -->
      <p i18n>Input Format*</p>
      <mat-form-field appearance="outline" class="file-format-dropdown">
        <mat-select formControlName="input_template_id">
          <mat-option [value]="template.template_id" *ngFor="let template of inputTemplates">{{ template.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <!-- output format template IDs -->
      <p i18n>Output Format*</p>
      <mat-form-field appearance="outline" class="file-format-dropdown">
        <mat-select formControlName="output_template_id">
          <mat-option [value]="template.template_id" *ngFor="let template of outputTemplates">{{ template.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <p i18n>Description</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <textarea matInput rows="5" i18n-placeholder placeholder="Type your text here…" formControlName="description"
          [errorStateMatcher]="matcher"></textarea>
        <mat-error *ngIf="uploadBatchForm.controls['description'].hasError('maxlength')" i18n>
          Description should not exceed 2000 characters
        </mat-error>
        <mat-error *ngIf="uploadBatchForm.controls['description'].hasError('pattern') || uploadBatchForm.controls['description'].hasError('whitespace')">Please enter a valid description </mat-error>
      </mat-form-field>
      <p i18n>References</p>
      <div formArrayName="reference">
        <ng-container *ngFor="
            let reference of references.controls;
            let i = index;
            let last = last
          ">
          <div class="ref-section" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="space-between center"
            style="margin-bottom: 12px">
            <mat-form-field class="" appearance="outline">
              <input matInput [formControlName]="i" [errorStateMatcher]="matcher" />

              <mat-error *ngIf="getLengthValidity(i)" i18n>
                Reference should not exceed 1000 characters
              </mat-error>
              <mat-error *ngIf="getContentValidity(i)" i18n>
                Please enter a valid reference
              </mat-error>
            </mat-form-field>

            <button mat-icon-button type="button" (click)="addReferences()" [disabled]="
                fileUploadStatus === 'Uploading' || checkRefFieldEmpty()
              " *ngIf="last">
              <mat-icon matTooltip="Add" matTooltipPosition="above">add</mat-icon>
            </button>
            <button mat-icon-button type="button" (click)="removeReferences(i)"
              [disabled]="fileUploadStatus === 'Uploading'">
              <mat-icon matTooltip="Remove" matTooltipPosition="above" *ngIf="references.controls.length > 1">remove
              </mat-icon>
            </button>
          </div>
        </ng-container>
        <mat-error *ngIf="refFlag" i18n>
          Maximum of 10 References can be added
        </mat-error>
      </div>

      <button [disabled]="
      fileUploadStatus === 'Uploading' ||
      files?.length == 0 ||
      uploadBatchForm.invalid
    " mat-raised-button class="sidePanel-upload-btn filled-btn-without-border" fxLayout fxLayoutAlign="center center"
        type="submit" i18n>
        {{ uploadButtonLabel }}
      </button>
    </form>
  </div>
  <!-- module chip -->

  <div *ngIf="addTag" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between center">
      <p class="panel-header" i18n>Add Tag</p>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-page/close.svg"
        (click)="closeSidepanel()" />
    </div>
    <div class="chip-container" fxLayout="row">
      <mat-chip-list selectable>
        <mat-chip *ngFor="let label of labelList" [matTooltip]="label.name" matTooltipPosition="above"
          [ngStyle]="{ 'background-color': label.bg_colour_code, 'color': label.text_colour_code, 'border': '1px solid #e4e4e4' }" (click)="addLabel(label.id, '')">
          {{ label.name }}
        </mat-chip>
      </mat-chip-list>
    </div>
    <hr />
    <form class="upload-form" [formGroup]="createLabelForm" (ngSubmit)="createLabel()">
      <p class="required-field" i18n>Create New Tag</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <input formControlName="name" maxlength="15" matInput i18n-placeholder placeholder="Type your text here..." />
        <mat-error *ngIf="createLabelForm.controls['name'].hasError('required')" i18n>
          Tag name is required
        </mat-error>
        <mat-error *ngIf="createLabelForm.controls['name'].hasError('pattern') || createLabelForm.controls['name'].hasError('whitespace')">Please enter a valid tag name </mat-error>
      </mat-form-field>
      <p class="required-field" i18n>Description</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <textarea formControlName="title" matInput rows="5" i18n-placeholder
          placeholder="Type your text here…"></textarea>
        <mat-error *ngIf="createLabelForm.controls['title'].hasError('required')" i18n>
          Tag description is required
        </mat-error>
        <mat-error *ngIf="createLabelForm.controls['title'].hasError('pattern') || createLabelForm.controls['title'].hasError('whitespace')">Please enter a valid description </mat-error>
      </mat-form-field>

      <!-- <div fxLayout="row" fxLayoutAlign="start start">
        <mat-chip-list selectable>
          <mat-chip class="tag-color-picker" *ngFor="let item of ['#96CEB4', '#FFEEAD', '#D9534F', '#FFAD60']"
            [ngStyle]="{ 'background-color': item }" (click)="toggleChip(item)" [selected]="chip.has(item)">
            <mat-icon class="iccon">done</mat-icon>
          </mat-chip>
        </mat-chip-list>
      </div> -->
      <!-- <p class="required-field" i18n>Tag Color</p>
      <div fxLayout="row" fxLayoutAlign="start start">
        <div *ngFor="
      let item of ['#96CEB4', '#FFEEAD', '#D9534F', '#FFAD60'];
      let i = index
        ">
          <div class="color-palette" [ngStyle]="{ 'background-color': item }" (click)="toggleTagColor(i, item)">
            <mat-icon class="icon" *ngIf="labelColor == item">done</mat-icon>
          </div>
        </div>
      </div> -->
      <!--  color picker component start -->
      <p i18n>Tag Decoration</p>
      <div fxLayout="column" fxLayoutAlign="start start" style="border: 1px solid #e4e4e4;
      border-radius: 4px;
      padding: 16px 16px;">
        <div fxFlex="row" style="width: 100%;margin-bottom: 17px;" fxLayoutGap="10px">
          <div fxLayout="column" class="tag-bg-color-palate" fxFlex="50">
            <p style="margin-top: 0px;" i18n>Background</p>
            <div #colorPickerHolder id="colorPicker"></div>
            <!-- <div fxLayout="row" fxLayoutAAlign="center center" fxLayoutGap="5px" style="margin: 9px 0px;">
              <div fxFlex="80" style="padding-top: 3px;">
                <input style="width: 100%;" type="range" id="opacitySlider" min="0" max="1" step="0.01" [(ngModel)]="opacity" [ngModelOptions]="{standalone: true}" (input)="updateColorDisplay(opacity)">
              </div>
              <div fxFlex="20" fxLayout="row" fxLayoutAlign="center center" style="border: 1px solid #e4e4e4;
              padding: 4px 0px;
              border-radius: 3px;">
                <span style="font-size: 10px;">{{opacity * 100 | number:'1.0-0'}}</span>
              </div>
            </div> -->
            <div>
            </div>
          </div>
          <div fxLayout="column" class="tag-text-color-palate" fxFlex="50">
            <p style="margin-top: 0px;" i18n>Text</p>
            <div #colorPickerHolder id="textColorPicker"></div>
            <!-- <div fxLayout="row" fxLayoutAAlign="center center" fxLayoutGap="5px" style="margin: 9px 0px;">
              <div fxFlex="80" style="padding-top: 3px;">
                <input style="width: 100%;" type="range" id="textOpacitySlider" min="0" max="1" step="0.01" [(ngModel)]="textOpacity" [ngModelOptions]="{standalone: true}" (input)="updateTextColorOpacity(textOpacity)">
              </div>
              <div fxFlex="20" fxLayout="row" fxLayoutAlign="center center" style="border: 1px solid #e4e4e4;
              padding: 4px 0px;
              border-radius: 3px;">
                <span style="font-size: 10px;">{{textOpacity * 100 | number:'1.0-0'}}</span>
              </div>
            </div> -->
          </div>
        </div>
        <div class="tag-style-preview-container" fxLayout="row" style="width: 100%;">
          <div fxLayout="row" fxLayoutAlign="center center" class="tag-style-preview-div" [ngStyle]="{'background-color': color, 'opacity': opacity}" style="width: 100%;height: 50px;border-radius: 30px;border: 1px solid gray;">
            <span [ngStyle]="{'color': textColor, 'opacity': textOpacity}">Tag preview</span>
          </div> 
        </div>
      </div>
      <!--  color picker component end -->

      <!-- <div fxLayout="row" fxLayoutAlign="center center" style="
      width: 101%;
      height: 40px;
      border: 1px solid gray;
      border-radius: 8px;
      margin-top: 8px;
      color: rgb(255, 255, 255);" [ngStyle]="{'background-color': color}">{{color}}
      </div> -->

      <!-- side panel upload button -->
      <button mat-raised-button class="sidePanel-upload-btn filled-btn-without-border" fxLayout
        fxLayoutAlign="center center" type="submit" [disabled]="createLabelForm.invalid || createLabelForm.pristine" i18n>
        Create Tag
      </button>
    </form>
  </div>

  <div *ngIf="batchLog" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between center">
      <p class="panel-header" i18n>Batch Log</p>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-page/close.svg"
        (click)="closeSidepanel()" />
    </div>
    <p *ngIf="logList == 'No logs found'">No logs found</p>
    <mat-stepper orientation="vertical" [linear]="false" #stepper *ngIf="!logsLoading">
      <mat-step *ngFor="let log of logList" [completed]="false" [editable]="false">
        <ng-template matStepLabel>
          <span innerHTML="{{ log.text | highlightWord : log.id }}"></span>
        </ng-template>
      </mat-step>
    </mat-stepper>
    <div class="loading-spinner center-placement" fxLayoutAlign="center center" *ngIf="logsLoading">
      <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
    </div>
  </div>
</ng-template>

<app-side-panel (onClose)="closeSidepanel()" [sidenavTemplateRef]="panelContent" [direction]="'right'" [navWidth]="380"
  [duration]="0.5">
</app-side-panel>

