<div class="help-wrapper" fxLayout="column" fxLayoutGap="30px">
  <!-- component header -->
  <form [formGroup]="helpForm">
    <div class="help-head" fxLayout="column" fxLayoutAlign="start start" fxLayoutGap="20px">
      <div class="help-page-client-logo" *ngIf="userInfo?.client_logo">
        <img src="{{ userInfo.client_logo }}" />
      </div>
    </div>

    <div class="help-body">
      <div fxLayout="column" fxFlex="50">
        <div fxLayout="column">
          <span class="form-field-heading" i18n>Email</span>
          <mat-form-field appearance="outline">
            <input matInput i18n-placeholder placeholder="<EMAIL>" formControlName="email" readonly/>
          </mat-form-field>
        </div>

        <div fxLayout="column">
          <span class="form-field-heading" i18n>Your feedback</span>
          <mat-form-field appearance="outline">
            <textarea matInput i18n-placeholder placeholder="Type your text here…" formControlName="feedback"></textarea>
          </mat-form-field>
        </div>

        <div class="btn-group" fxLayout="row" fxLayoutGap="10px" fxFlex="100">
          <button style="text-align: center" class="theme-black-stroked-btn" mat-stroked-button (click)="resetForm()" i18n>
            Cancel
          </button>

          <button class="theme-blue-raised-btn save" color="primary" mat-raised-button type="submit"
            [disabled]="helpForm.invalid" (click)="helpFormSubmit()" i18n>
            Save
          </button>
        </div>
      </div>
    </div>
  </form>
</div>
