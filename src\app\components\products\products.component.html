<div class="wrapper" fxFlex="100" fxLayout="column">
  <div class="filter-container" fxLayout="column" fxLayoutAlign="center start">
    <div class="filter-head" fxLayoutAlign="space-between start">
      <div fxLayout="row" fxLayoutGap="10px" class="search-container" fxFlex="30">
        <mat-chip-list #chipList> </mat-chip-list>
        <mat-form-field fxFlex="70" fxFlex.gt-md="90" appearance="none" class="search-filter">
          <input id="search" matInput i18n-placeholder placeholder="Search SKU, Product Name, Batch ID" i18n-placeholder
            [matChipInputFor]="chipList" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            (matChipInputTokenEnd)="addQuery($event.value)" [(ngModel)]="search" #searchChips />
          <mat-icon matPrefix class="search-icon" (click)="addQuery(search)">search</mat-icon>
        </mat-form-field>
      </div>

        <div class="btn-container" fxLayout="row" fxLayoutGap="10px" fxFlex="70">
          <mat-form-field appearance="none" class="date-range-filter">
            <mat-select [(value)]="selected" (selectionChange)="getDataForDate($event.value)">
              <div *ngFor="let option of datePickerOptions">
                <!-- Recent -->
                <mat-option *ngIf="option.value === 'recent'" value="recent">
                  <div fxLayout="column" class="range-category" *ngIf="option.value === 'recent'" fxLayout="row">
                    <span i18n> Recent</span> &nbsp;
                    <span class="date-range"> </span>
                  </div>
                </mat-option>
                <!-- Last Week / Month / Quarter -->
                <mat-option [value]="option.value" *ngIf="
                    option.value !== 'recent' && option.value !== 'custom_range'
                  ">
                  <div fxLayout="column" class="range-category">
                    {{ option.display }}
                    <span class="date-range">
                      {{ option.start_date | date: "mediumDate" }} -
                      {{ currentDate | date: "mediumDate" }}</span>
                  </div>
                </mat-option>
                <!-- Custom range  -->
                <mat-option *ngIf="option.value === 'custom_range'" value="custom_range" (click)="picker.open()">
                  <div fxLayout="column" >
                    <div fxLayout="row" class="range-category" fxLayoutAlign="start center">
                        <span i18n>Custom Range </span>
                        <mat-date-range-input [rangePicker]="picker" [min]="minDate" [max]="maxDate"
                            style="display: none">
                            <input type="hidden" matStartDate #dateRangeStart [(ngModel)]="customStartDate" />
                            &nbsp;
                            <input type="hidden" matEndDate #dateRangeEnd [(ngModel)]="customEndDate" (dateChange)="
                                dateRangeChange(dateRangeStart, dateRangeEnd)
                            " />
                         </mat-date-range-input>
                         <span fxLayout style="margin: 0 0 0 8px">
                    
                            <!-- <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle> -->
                            <mat-date-range-picker class="custom-date-icon" #picker></mat-date-range-picker>
                        </span>
                    </div>
                    <span class="date-range" *ngIf="customStartDate && customEndDate">
                    {{ customStartDate | date: "mediumDate" }} -
                    {{ customEndDate | date: "mediumDate" }}</span>     
                </div>
                </mat-option>
              </div>
            </mat-select>
            <div class="date-range-icon">
              <img src="assets/images/calender.svg" />
            </div>
          </mat-form-field>

          <!-- product filters -->
          <mat-form-field appearance="none" class="product-filter"
          *ngFor="let item of productFilterList; let i = index">
          <mat-select placeholder="{{ item.display_name }}" [(ngModel)]="selectedProducts[i]"
            (ngModelChange)="getProductSelection()" name="selectedProducts" multiple>
            <mat-option #mulVal *ngFor="let mulVals of item.values" [value]="mulVals">
              {{ mulVals }}
            </mat-option>
          </mat-select>
          </mat-form-field>

          <!-- reset button -->
          <button mat-button fxLayout fxLayoutAlign="center center" class="reset-btn filled-btn-primary"
            (click)="resetFilters()" i18n-matTooltip matTooltip="Clear search and filters" matTooltipPosition="above"
            i18n>
            Reset
          </button>
          <div class="view-messages-icon" i18n-matTooltip matTooltip="View comments" matTooltipPosition="above"
            [routerLink]="'/details/comments'" [queryParams]="{sub: subscriptionId, origin: '/products'}">
            <!-- <div class="dot"></div> -->
            <img src="assets/images/message.svg" />
          </div>
        </div>
    </div>
    <!-- mat chip for search attr -->
    <div>
      <mat-chip class="search-chip" *ngFor="let item of filterList"
        [ngStyle]="{'background-image': item.is_batch ? 'linear-gradient(to right, #c5d9f3 '+item.progress+'%, white '+item.progress+'%)' : 'none' }"
        [selectable]="selectable" [removable]="removable" (removed)="removeQuery(item)"
        [matTooltip]="item.is_batch ? item.progress +'%': null" [matTooltipPosition]="'above'">
        {{ decode(item.value) }}
        <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
      </mat-chip>
    </div>
  </div>

  <div class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px">
    <mat-tab-group (selectedTabChange)="bucketTabChange($event)" [selectedIndex]="selectedIndex">
      <mat-tab [label]="tab.value" *ngFor="
          let tab of tabs
        ">
        <ng-template mat-tab-label>
          <span matTooltip="{{tab.desc}}" matTooltipPosition="above">{{ tab.name }} </span>&nbsp;
          <span class="batch-count" fxLayoutAlign="center center" *ngIf="bucketWiseCount">
            {{ bucketWiseCount[tab.slug] }}</span>
        </ng-template>
        <div class="mat-elevation-z8" class="table-section">
          <!-- table -->
          <table mat-table [dataSource]="dataSource" *ngIf="productTableData && !tableDataLoading">

            <ng-container matColumnDef="{{ item.slug }}" *ngFor="let item of productHeaderData">
              <div *ngIf="item.slug != 'actions'">
                <th mat-header-cell *matHeaderCellDef>
                  {{ item.display_name | underscoreAsSpace }}
                </th>
              </div>
              <div *ngIf="item.slug === 'actions'" fxFlex="10">
                <th mat-header-cell *matHeaderCellDef>
                  <span *ngIf="permissionsObject[tab.slug]?.length > 0" i18n>
                    Actions
                  </span>
                </th>
              </div>
              <td mat-cell *matCellDef="let element">
                <!-- row id column -->
                <div fxLayout="column" *ngIf="item.slug == 'row_id'">
                  <a fxLayout="row" class="batch-id text-theme-primary" (click)="storePageAndSize()"
                    [routerLink]="userViewPreference == 'product-details' ? ['/details/product-details'] : ['/details/review-mode']"
                    [queryParams]="{row_id: element['row_id'], bucket: element.bucket['value'], from: 'products', sub: subscriptionId}">
                    {{ element['row_id'] }}
                  </a>
                  <span class="batch-chip chip">{{ element['batch_id'] }}</span>
                  <span fxLayout="row" class="batch-date">
                    {{ element['created_at'] | date: 'y/MM/dd'}}
                    <!-- <mat-icon class="batch-info">info_outline</mat-icon> -->
                  </span>
                </div>
                <!-- actions column -->
                <div *ngIf="
                    item.slug === 'actions' &&
                    permissionsObject[tab.slug]?.length > 0
                  ">
                  <mat-form-field appearance="none" fxFlex="70" class="move_to_frmField">
                    <mat-select i18n-placeholder placeholder="Move To" [disableOptionCentering]="true">
                      <mat-option style="margin: 8px 0 8px 0px" *ngFor="let bucketName of permissionsObject[tab.slug]"
                        [value]="bucketName.value" (click)="updateBucket(element['row_id'], bucketName.value)">
                        {{ bucketName.display_name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <!-- other columns -->
                <div fxLayout="column"
                  *ngIf="item.slug != 'row_id' && item.slug != 'actions' && element[item.slug] != null" [matTooltip]="
                element[item.slug]?.length > 20
                  ? element[item.slug]
                  : null
                ">
                  <p>{{ element[item.slug] | truncate: 20 }}</p>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
          <!-- progress spinner -->
          <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="tableDataLoading">
            <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
          </div>
          <!-- no data section -->
          <div class="no-data" *ngIf="!tableDataLoading && productTableData.length == 0" fxLayout="row"
            fxLayoutGap="10px">
            <mat-icon fontSet="material-icons-outlined">info</mat-icon>
            <span i18n>Nothing to display</span>
          </div>
          <mat-paginator [length]="totalItems" [pageSize]="size" [pageIndex]="page - 1"
            *ngIf="productTableData.length > 0" [pageSizeOptions]="[10, 20, 50, 100]" (page)="onPaginateChange($event)"
            showFirstLastButtons>
          </mat-paginator>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
