<span fxLayoutAlign="space-between center" class="undo-span">
    {{response | underscoreAsSpace}}
    <mat-icon class="btn-spinner" *ngIf="loading"
    ><mat-spinner color="accent" diameter="20"> </mat-spinner
  ></mat-icon>
    <button *ngIf="!loading" mat-flat-button (click)="undoCall()" i18n>
        UNDO
    </button>
    <span *ngIf="!loading">
        <mat-icon (click)="snackBarRef.dismiss()">close</mat-icon>
    </span>
  </span>
