import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SharedModule } from './shared/shared.module';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { NgChartsModule } from 'ng2-charts';
//http imports
import {
  HttpClientModule,
  HTTP_INTERCEPTORS,
  HttpClientXsrfModule,
} from '@angular/common/http';
import { HttpConfigInterceptor } from './_interceptors/http-config.interceptor';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { HomeComponent } from './components/home/<USER>';
//pipe
import { SafeContentPipe } from './_pipe/safe-content.pipe';
import { HighlightWordPipe } from './_pipe/highlight-word.pipe';
//components
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { TopNavComponent } from './components/header/top-nav/top-nav.component';
import { SideNavComponent } from './components/header/side-nav/side-nav.component';
import { SidePanelComponent } from './components/side-panel/side-panel.component';
import { ProductsComponent } from './components/products/products.component';
import { ReviewComponent } from './components/review/review.component';
import { SettingsComponent } from './components/settings/settings.component';
import { AuthHttpInterceptor, AuthModule } from '@auth0/auth0-angular';
import { environment } from '../environments/environment';
import { LoadingComponent } from './components/loading/loading.component';
import { UndoSnackbarComponent } from './_dialogs/undo-snackbar/undo-snackbar.component';
import { HelpComponent } from './components/help/help.component';
import { ReferenceUrlDialogComponent } from './_dialogs/reference-url-dialog/reference-url-dialog.component';
import { OfflineComponent } from './components/offline/offline.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import {
  NgxMatDatetimePickerModule,
  NgxMatNativeDateModule,
  NgxMatTimepickerModule,
} from '@angular-material-components/datetime-picker';
import 'moment/locale/ja';
import 'moment/locale/en-au';
import { ConfirmDialog } from './_dialogs/confirm-dialog/confirm-dialog.component';
import { ApproveBatchDialog } from './_dialogs/approve-batch/approve-batch.component';


@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    SafeContentPipe,
    TopNavComponent,
    SideNavComponent,
    SidePanelComponent,
    ProductsComponent,
    ReviewComponent,
    SettingsComponent,
    LoadingComponent,
    UndoSnackbarComponent,
    HelpComponent,
    ReferenceUrlDialogComponent,
    HighlightWordPipe,
    OfflineComponent,
    ConfirmDialog,
    ApproveBatchDialog,
  
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    SharedModule,
    AppRoutingModule,
    NgxDropzoneModule,
    NgChartsModule,
    MatDatepickerModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    NgxMatNativeDateModule,
    AuthModule.forRoot({
      domain: environment.auth0.domain,
      clientId: environment.auth0.clientId,
      redirectUri: window.location.origin + '/loading',
      useRefreshTokens: environment.auth0.useRefreshTokens,
      audience: environment.auth0.audience,
      prompt: 'none',
      httpInterceptor: {
        allowedList: ['/api/*'],
      },
    }),
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthHttpInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpConfigInterceptor,
      multi: true,
    },
    // AppPermissionsService,
    // { provide: MAT_DATE_LOCALE, useValue: 'en-GB' },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
