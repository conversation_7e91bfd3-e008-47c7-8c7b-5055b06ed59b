import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'comment',
})
export class CommentPipe implements PipeTransform {
  transform(value: string, comment): string {
    // if comment has embedded image, no value
    let result = this.extractContent(value);
    if ((/<p>\s/.test(value) ||  value.startsWith('<p><br></p>') || value.startsWith('<h1><br></h1>') || value.startsWith('<h2><br></h2>')) && result.length === 0) {
      return '<i>' + 'Added an image' + '</i>';
    }
    // if comment has embedded image
    if (value.startsWith('<p><img')) {
      return '<i>' + 'Added an image' + '</i>';
    }
    // if comment has embedded link
    if (value.startsWith('<p><a')) {
      return '<i>' + 'Added a link' + '</i>';
    }
    // if comment has attachment
    let res = this.extractContent(value);
    if (comment['attachment'].hasOwnProperty('name') && res.length === 0) {
      return '<i>' + 'Added an attachment' + '</i>';
    }
    // if comment exceeds the char limit
    let charlimit = 35;
    if (res && res.length > charlimit) {
      let adding_spaces = value.replace(/<br[^>]*>/g, '');
      let without_html = adding_spaces.replace(/<(?:.|\n)*?>/gm, '');
      let shortened = without_html.substring(0, charlimit) + '...';
      return shortened;
    }
    // else
    return value
      .replace(/<p[^>]*>/g, ' <span>')
      .replace(/<\/p[^>]*>/g, '</span>')
      .replace(/<br[^>]*>/g, '');
  }

  extractContent(value) {
    let span = document.createElement('div');
    span.innerHTML = value;
    return span.textContent.trim() || span.innerText.trim();
  }
}
