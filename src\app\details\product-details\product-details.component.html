<!-- progress spinner -->
<div class="loading-spinner" fxLayoutAlign="center center" *ngIf="tableDataLoading">
  <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
</div>

<div class="wrapper" fxFlex="100" fxLayout="column" *ngIf="!tableDataLoading">
  <!-- top toolbar -->
  <div class="filter-container" style="height: 80px">
    <div class="filter-head" fxLayoutAlign="space-between center" fxLayout="row">
      <div fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-between center">
        <div class="back-icon" routerLink="/{{ previousPageUrl }}"
          [queryParams]="{ sub: subscriptionId, bucket: bucket,  action: 'back' }">
          <mat-icon>west</mat-icon>
        </div>
        <div class="product-header" fxLayoutAlign="space-between center">
          <span
            [matTooltip]="productTitle && productTitle.length > 60 ? productTitle : ''">{{ productTitle | truncate: 60 }}</span>
        </div>
      </div>

      <!-- next previous btn  -->
      <div class="action-btn" fxLayoutGap="10px" fxLayout="row" fxLayoutAlign="space-between center">
        <button mat-button class="update-btn" (click)="updateFieldsOnClick()" i18n>
          Update
        </button>
        <button mat-button class="previous-btn" (click)="previousPage(previousRowId)" matTooltip="Previous" matTooltipPosition="above"  i18n-matTooltip>
          <mat-icon matPrefix>west</mat-icon>
        </button>
        <button mat-button class="previous-btn" (click)="nextPage(nextRowId)" matTooltip="Next" matTooltipPosition="above"  i18n-matTooltip>
          <mat-icon matPrefix>east</mat-icon>
        </button>
        <div class="view-messages-icon" i18n-matTooltip matTooltip="Comments" matTooltipPosition="above" [routerLink]="'/details/comments'"
          [queryParams]="{sub: subscriptionId, origin: '/details/product-details', row_id: row_id, bucket: bucket }">
          <!-- <div class="dot"></div> -->
          <img src="assets/images/message.svg" />
        </div>

        <mat-slide-toggle class="text-theme-primary" [checked]="productModeChecked" (change)="toggleSwitches($event)"
          i18n>
          Product Details
        </mat-slide-toggle>
      </div>
    </div>
  </div>

  <!-- product details info card -->
  <div fxLayout="column" class="table-wrapper" style="margin-right: 40px;" fxFlex="100">
    <mat-card class="product-info-container" fxLayoutAlign="space-between start" fxLayout="row">
      <div class="product-image" fxLayout="row">
        <div class="prod-img" fxLayout="column" *ngIf="productImg?.length > 0" (click)="openImageSlider(productImg)">
          <span class="overlay">
            <img fxLayout src="{{ productImg[0].value }}" class="prod-img" />
            <h3>
              <span fxLayout class="image-count">
                {{ productImg.length }}
                <mat-icon style="margin-left: 3px">image</mat-icon>
              </span>
            </h3>
          </span>
        </div>
        <div class="prod-ids" fxLayoutGap="100px" fxLayout="row">
          <div class="product-details" fxLayout="column" fxLayoutGap="30px">
            <span><span class="bold" i18n>Row ID :</span> {{ productIds?.row_id }}</span>
            <span>
              <span class="bold" i18n>Batch ID :</span> {{ productIds?.batch_id }}
            </span>
            <div class="tag active" [ngStyle]="
                productBucket == 'rework' && { 'background-color': 'red' }
              ">
              <p>{{ productBucket | titlecase | underscoreAsSpace }}</p>
            </div>
          </div>
          <div fxLayout="column" class="product-details-row" fxLayoutGap="30px">
            <span *ngFor="let item of productDetailsData.classification_attributes"><span class="bold">{{item.name}}
                :</span> {{item.values}}</span>
          </div>
        </div>
      </div>
      <!-- bucket update action -->
      <div class="move_to_frmField" fxLayout="row" *ngIf="moveToBucketList && moveToBucketList.length > 0">
        <mat-form-field appearance="none">
          <mat-select i18n-placeholder placeholder="Move To" [disableOptionCentering]="true">
            <mat-option *ngFor="let bucket of moveToBucketList" (click)="updateBucket(rowId, bucket.value)">
              {{ bucket.display_name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </mat-card>
    <!-- details tab  -->
    <mat-tab-group class="tab-container vertical-tab-container">
      <mat-tab [label]="tabHeader.name" *ngFor="let tabHeader of tabList">
        <mat-card class="product-field" fxLayout="row">
          <!-- no data section -->
          <!-- <div fxFlex="60"
            *ngIf="
              !productDetailsData[tabHeader.slug] ||
              productDetailsData[tabHeader.slug].length == 0"
            fxLayoutAlign="center center"
            class="no-data"
          >
            <mat-icon fontSet="material-icons-outlined">info</mat-icon>&nbsp;
            <span class="no-pred-found"> No Data available </span>
          </div> -->

          <div fxLayout="column" fxFlex="50">
            <div class="tab-attr" fxLayout="column" *ngFor="
                let attribute of productDetailsData[tabHeader.slug];
                let i = index
              ">
              <!-- for text data type -->
              <div class="mat-form-field mat-form-field-appearance-outline" fxFlex="100"
                *ngIf="attribute.data_type === 'TEXT'">
                <mat-form-field appearance="outline" [ngClass]="{ edit: attribute.needs_editing }" floatLabel="always"
                  fxFlex="81">
                  <mat-label>{{ attribute.name }}</mat-label>
                  <input matInput fxFlex="95" value="{{ attribute.values }}" [(ngModel)]="attribute.values"
                    [readonly]="!attribute.is_editable" (change)="
                      onChangeOfInput(attribute.prediction_id, attribute.values)
                    " />
                  <mat-icon *ngIf="!attribute.is_editable" class="lock-icon" i18n-matTooltip matTooltip="Read only"
                    matTooltipPosition="above">lock</mat-icon>
                </mat-form-field>
                <!-- <mat-icon fxFlex="10" fxFlex.lt-md="1" fxFlex.lt-sm="1" *ngIf="attribute.existing !== null"
                  class="existing-icon" matTooltip="{{
                    attribute?.existing.toString() | commmaSeperator
                  }}" [matTooltipPosition]="'above'">info
                </mat-icon> -->
              </div>

              <!-- for AUTOCOMPLETE_SINGLE_SELECT data type -->
              <div class="mat-form-field mat-form-field-appearance-outline" [ngClass]="
                  attribute.prediction_id &&
                  attribute.prediction_id === prediction_id
                    ? 'mat-focused'
                    : false
                " fxFlex="100" *ngIf="attribute.data_type === 'AUTOCOMPLETE_SINGLE_SELECT'">
                <mat-form-field appearance="outline" [ngClass]="{ edit: attribute.needs_editing }" floatLabel="always"
                  fxFlex="81">
                  <mat-label>{{ attribute.name }}</mat-label>
                  <input type="text" value="{{ attribute.values }}" [(ngModel)]="attribute.values"
                    [readonly]="!attribute.is_editable" matInput [formControl]="myControl" [matAutocomplete]="auto"
                    (ngModelChange)="autocompleteAttrSlug = attribute.slug" />
                  <mat-spinner matSuffix diameter="20" *ngIf="optionsLoading"></mat-spinner>
                  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="
                      onChangeOfInput(
                        attribute.prediction_id,
                        $event.option.value
                      )
                    ">
                    <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
                      {{ option }}
                    </mat-option>
                    <mat-option disabled *ngIf="
                        autoCompleteSuggestions?.length === 0 && !optionsLoading
                      " i18n>
                      No Data found
                    </mat-option>
                  </mat-autocomplete>
                </mat-form-field>
                <mat-icon fxFlex="10" fxFlex.lt-md="1" fxFlex.lt-sm="1" *ngIf="attribute.existing !== null"
                  class="existing-icon" matTooltip="{{
                    attribute.existing.toString() | commmaSeperator
                  }}" [matTooltipPosition]="'above'">info
                </mat-icon>
              </div>

              <!-- for ASSET_LINK data type -->
              <div class="mat-form-field mat-form-field-appearance-outline" [ngClass]="
                  attribute.prediction_id &&
                  attribute.prediction_id === prediction_id
                    ? 'mat-focused'
                    : false
                " fxFlex="100" *ngIf="attribute.data_type === 'ASSET_LINK'">
                <mat-form-field appearance="outline" [ngClass]="{ edit: attribute.needs_editing }" floatLabel="always"
                  fxFlex="81">
                  <mat-label>{{ attribute.name }}</mat-label>
                  <input matInput fxFlex="95" value="{{ attribute.values }}" [(ngModel)]="attribute.values"
                    [readonly]="!attribute.is_editable" (change)="
                      onChangeOfInput(attribute.prediction_id, attribute.values)
                    " />
                  <a class="mapped-asset" *ngIf="attribute.mapped_asset && attribute.mapped_asset != ''"
                    href="{{ attribute.mapped_asset }}" target="_blank">
                    <mat-icon>link</mat-icon>
                  </a>
                  <mat-icon *ngIf="!attribute.is_editable" class="lock-icon" matTooltip="Read only"
                    matTooltipPosition="above">lock</mat-icon>
                </mat-form-field>
                <mat-icon fxFlex="10" fxFlex.lt-md="1" fxFlex.lt-sm="1" *ngIf="attribute.existing !== null"
                  class="existing-icon" matTooltip="{{
                    attribute.existing.toString() | commmaSeperator
                  }}" [matTooltipPosition]="'above'">info
                </mat-icon>
              </div>

              <!-- for data type Link -->
              <div class="assets-div" *ngIf="attribute.data_type === 'LINK'" fxFlex="100">
                <mat-label class="label">{{ attribute.name }} : </mat-label>
                <a [href]="item" target="_blank" *ngFor="let item of attribute.values">
                  {{ item }}
                </a>
              </div>

              <div class="mat-form-field mat-form-field-appearance-outline" [ngClass]="
                  attribute.prediction_id &&
                  attribute.prediction_id === prediction_id
                    ? 'mat-focused'
                    : false
                " fxFlex="100" *ngIf="attribute.data_type === 'CLOSED_LIST_SINGLE_SELECT_STATIC'">
                <!-- For type CLOSED_LIST_SINGLE_SELECT_STATIC-->
                <mat-form-field appearance="outline" [ngClass]="{ edit: attribute.needs_editing }" fxFlex="81" *ngIf="
                    attribute.data_type === 'CLOSED_LIST_SINGLE_SELECT_STATIC'
                  ">
                  <mat-label>{{ attribute.name }} closed list </mat-label>
                  <mat-select [(value)]="attribute.values[0]" (selectionChange)="
                      onChangeOfInput(
                        attribute.prediction_id,
                        attribute.values[0]
                      )
                    ">
                    <mat-option *ngFor="let choices of attribute.choices" [value]="choices">
                      {{ choices }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-icon fxFlex="10" fxFlex.lt-md="1" fxFlex.lt-sm="1" *ngIf="attribute.existing !== null"
                  class="existing-icon" matTooltip="{{
                    attribute.existing.toString() | commmaSeperator
                  }}" [matTooltipPosition]="'above'">info
                </mat-icon>
              </div>
            </div>
          </div>

          <!-- <div class="loading-comments"
          fxLayoutAlign="center center"
          *ngIf="commentsLoading && commentThread.length == 0"
          fxFlex="100"
        >
          <mat-spinner
            fxLayoutAlign="center center"
            diameter="40"
            strokeWidth="3"
          ></mat-spinner>
        </div> -->
          <!-- comment thread scroll container -->
          <div class="product-detail-comments" fxFlex="50" #scrollContainer fxLayout="column" fxLayoutGap="20px"
            fxLayoutAlign="start center" infiniteScroll [scrollWindow]="false" [infiniteScrollDistance]="1"
            [infiniteScrollUpDistance]="1" [infiniteScrollThrottle]="50" (scrolled)="onCommentThreadScroll()">
            <!-- comment area -->
            <div class="comment-box" fxLayout="row">
              <div fxLayout="column" class="cmt-textarea" fxLayoutAlign="start">
                <!-- post comment -->
                <div [ngClass]="{'editor-fs':isFullscreen}" #fullScreen>
                  <quill-editor [placeholder]=editorPlaceholder [styles]="editorStyle" [modules]="modules"
                    [(ngModel)]="content" theme="snow" (onContentChanged)="onContentChange($event, 'create')" 
                    (onEditorCreated)="getEditorInstance($event)" customToolbarPosition="bottom">
                    <div quill-editor-toolbar>
                      <span class="ql-formats">
                        <button class="ql-bold" i18n-matTooltip matTooltip="Bold" matTooltipPosition="above"></button>
                        <button class="ql-italic" i18n-matTooltip matTooltip="Italic"
                          matTooltipPosition="above"></button>
                        <button class="ql-underline" i18n-matTooltip matTooltip="Underline"
                          matTooltipPosition="above"></button>
                        <button class="ql-strike" i18n-matTooltip matTooltip="Strikethrough"
                          matTooltipPosition="above"></button>
                        <button class="ql-blockquote" i18n-matTooltip matTooltip="Quote"
                          matTooltipPosition="above"></button>
                        <button class="ql-code" i18n-matTooltip matTooltip="Insert code"
                          matTooltipPosition="above"></button>
                        <select class="ql-color" title="Text colour" i18n-title></select>
                        <button class="ql-header" value="1" i18n-matTooltip matTooltip="Heading 1"
                          matTooltipPosition="above"></button>
                        <button class="ql-header" value="2" i18n-matTooltip matTooltip="Heading 2"
                          matTooltipPosition="above"></button>
                        <button class="ql-list" value="ordered" i18n-matTooltip matTooltip="Numbered list"
                          matTooltipPosition="above"></button>
                        <button class="ql-list" value="bullet" i18n-matTooltip matTooltip="Bullet list"
                          matTooltipPosition="above"></button>
                        <button class="ql-link" i18n-matTooltip matTooltip="Insert link"
                          matTooltipPosition="above"></button>
                        <!-- <button class="ql-image" i18n-matTooltip matTooltip="Insert image"
                          matTooltipPosition="above"></button> -->
                          <input style="display: none" type="file" accept="image/*" (change)="insertImage($event)"
                            #fileInput>
                          <button (click)="fileInput.click()" *ngIf="!isImgUpload">
                            <mat-icon style="font-size: 18px;margin: -1px; color:#444444; padding-top: 2px;"
                              i18n-matTooltip matTooltip="Insert image" matTooltipPosition="above">collections</mat-icon>
                          </button>
                          <button *ngIf="isImgUpload">
                            <mat-progress-spinner color="primary" mode="indeterminate" diameter="20">
                            </mat-progress-spinner>
                          </button>
                        <button class="ql-attachment" *ngIf="files.length == 0" i18n-matTooltip
                          matTooltip="Attach a file" matTooltipPosition="above">
                          <label for="inputTag">
                            <mat-icon style="font-size: 22px;margin: -1px; color:#444444 !important;"
                              class="attach-file">attachment</mat-icon>
                            <input id="inputTag" type="file" style="display: none;" [(ngModel)]="currentInput"
                              (change)="onFileSelected($event, 'create')" />
                          </label>
                        </button>
                        <button (click)="toggleFullscreen(!isFullscreen, 'create')" class="ql-fullscreen">
                          <mat-icon *ngIf="!isFullscreen" style="font-size: 18px;margin: -1px; color:#444444"
                            matTooltip="Go fullscreen" i18n-matTooltip matTooltipPosition="above">open_in_full</mat-icon>
                          <mat-icon *ngIf="isFullscreen" style="font-size: 18px;margin: -1px; color:#444444"
                            matTooltip="Close fullscreen" matTooltipPosition="above">close_fullscreen</mat-icon>
                        </button>
                      </span>
                    </div>
                  </quill-editor>
                </div>
                <div class="box-footer" fxLayout="row" fxLayoutAlign="space-between center" style="margin-top: 10px;">
                  <!-- <mat-icon class="text-theme-primary attach-file">attachment</mat-icon> -->
                  <div class="chip-row">
                    <ng-container *ngIf="uploadCheck">
                      <div class="file-chip" fxLayout="row" fxLayoutAlign="space-between center" *ngFor="let file of files let i = index">
                        <span>{{file.name}}</span>
                        <mat-icon (click)="clear(i)">cancel</mat-icon>
                      </div>
                    </ng-container>
                  </div>
                  <button mat-raised-button class="filled-btn-primary" (click)="handler('create')"
                    [disabled]="selectedUsers.length <= 0 && (content == '' || content == null) && files.length <= 0"
                    i18n>
                    Send
                  </button>
                </div>
              </div>
            </div>
            <!-- comments scroll -->
            <div class="cmt-profile" *ngFor="let comment of commentThread; let i = index" fxLayoutAlign="center start">
              <!-- show edit/delete on comment hover -->
              <!-- preview comment -->
              <div fxLayout="row" [class.hover-color]="row === i" *ngIf="!comment.editable"
                fxLayoutAlign="space-between start" fxFlex="100" (mouseover)="
            userData.username == comment.created_by_username
              ? (row = i)
              : (row = -1)
          " (mouseleave)="row = -1">
                <!-- comment profile picture -->
                <img src="{{ comment.profile_picture }}" />
                <div fxLayout="column" class="cmt-profile-picture">
                  <!-- comment header -->
                  <div fxLayout="row" class="comment-head" fxLayoutAlign="space-between center">
                    <div fxLayout="column">
                      <span class="username">{{ comment.created_by }}</span>
                      <span class="comment-time">
                        {{ comment.created_at | date: 'y/MM/dd' }}
                      </span>
                    </div>
                    <span style="padding-left: 20px;" fxLayout="row" fxLayoutGap="5px" *ngIf="
                    userData.username == comment.created_by_username && row == i
                ">
                      <img src="../../../assets/images/comments-page/edit.svg"
                        (click)="editComment(comment); comment.editable = true" i18n-matTooltip matTooltip="Edit"
                        matTooltipPosition="above" />
                      <img class="delete-icon" src="../../../assets/images/comments-page/trash.svg"
                        (click)="deleteComment(comment.comment_id, i)" i18n-matTooltip matTooltip="Delete"
                        matTooltipPosition="above" />
                    </span>
                  </div>

                  <!-- comment content -->
                  <div class="comments" fxLayoutAlign="space-between center" fxFlex="100">
                    <div>
                      <div class="comment-text editor-comments-img">
                        <span [innerHTML]="comment.text | innerHTMLstyles"></span>
                      </div>
                      <!-- <span class="comment-text">{{comment.attachment.name}}</span> -->
                      <div fxLayout="row" *ngIf="comment.attachment.name" class="chip-row"
                        [matTooltip]="comment.attachment.name" matTooltipPosition="above">
                        <a [href]="comment.attachment.signed_url">
                          <span class="file-chip">{{comment.attachment.name | truncate:8}}
                          </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- edit comment part -->
              <div class="edit-comment-box" fxLayout="column" *ngIf="comment.editable" style="width: 100%">
                <div fxLayout="row" fxLayoutAlign="space-between start" fxFlex="100">
                  <!-- profile picture -->
                  <img src="{{ comment.profile_picture }}" />
                  <div fxLayout="column" class="cmt-profile-picture">
                    <!-- comment header -->
                    <div fxLayout="row" class="comment-head" fxLayoutAlign="space-between center">
                      <div fxLayout="column">
                        <span class="username">{{ comment.created_by }}</span>
                        <span class="comment-time">
                          {{ comment.created_at | date: 'y/MM/dd' }}
                        </span>
                      </div>
                    </div>
                    <!-- edit comment editor -->
                    <div [ngClass]="{'editor-fs':isEditFullscreen}" #fullScreenEdit style="margin-top: 5px">
                      <quill-editor [placeholder]=editorPlaceholder [styles]="editorStyle" [modules]="editModuleconfig"
                        [(ngModel)]="comment.edited_text" theme="snow"
                        (onContentChanged)="onContentChange($event, 'edit', comment)"
                        (onEditorCreated)="getEditorInstance($event,'edit')" customToolbarPosition="bottom">
                        <div quill-editor-toolbar>
                          <span class="ql-formats">
                            <button class="ql-bold" i18n-matTooltip matTooltip="Bold"
                              matTooltipPosition="above"></button>
                            <button class="ql-italic" i18n-matTooltip matTooltip="Italic"
                              matTooltipPosition="above"></button>
                            <button class="ql-underline" i18n-matTooltip matTooltip="Underline"
                              matTooltipPosition="above"></button>
                            <button class="ql-strike" i18n-matTooltip matTooltip="Strikethrough"
                              matTooltipPosition="above"></button>
                            <button class="ql-blockquote" i18n-matTooltip matTooltip="Quote"
                              matTooltipPosition="above"></button>
                            <button class="ql-code" i18n-matTooltip matTooltip="Insert code"
                              matTooltipPosition="above"></button>
                            <select class="ql-color" i18n-title title="Text colour"></select>
                            <button class="ql-header" value="1" i18n-matTooltip matTooltip="Heading 1"
                              matTooltipPosition="above"></button>
                            <button class="ql-header" value="2" i18n-matTooltip matTooltip="Heading 2"
                              matTooltipPosition="above"></button>
                            <button class="ql-list" value="ordered" i18n-matTooltip matTooltip="Numbered list"
                              matTooltipPosition="above"></button>
                            <button class="ql-list" value="bullet" i18n-matTooltip matTooltip="Bullet list"
                              matTooltipPosition="above"></button>
                            <button class="ql-link" i18n-matTooltip matTooltip="Insert link"
                              matTooltipPosition="above"></button>
                            <!-- <button class="ql-image" i18n-matTooltip matTooltip="Insert image"
                            matTooltipPosition="above"></button> -->
                            <input style="display: none" type="file" accept="image/*"
                              (change)="insertImage($event, 'edit')" #fileInputEdit>
                            <button (click)="fileInputEdit.click()" *ngIf="!isImgUploadEdit">
                              <mat-icon style="font-size: 18px;margin: -1px; color:#444444; padding-top: 2px;"
                                i18n-matTooltip matTooltip="Insert image" matTooltipPosition="above">collections
                              </mat-icon>
                            </button>
                            <button *ngIf="isImgUploadEdit">
                              <mat-progress-spinner color="primary" mode="indeterminate" diameter="20">
                              </mat-progress-spinner>
                            </button>
                            <button class="ql-attachment" i18n-matTooltip
                              *ngIf="isAllowAttachment && filesEdit?.length == 0" matTooltip="Attach a file"
                              matTooltipPosition="above">
                              <label for="inputTag2">
                                <mat-icon style="font-size: 22px;margin: -1px; color:#444444 !important;"
                                  class="attach-file">attachment</mat-icon>
                                <input id="inputTag2" type="file" style="display: none;"
                                  (change)="onFileSelected($event, 'edit')" />
                              </label>
                            </button>
                            <button (click)="toggleFullscreen(!isEditFullscreen, 'edit')" class="ql-fullscreen">
                              <mat-icon *ngIf="!isEditFullscreen" style="font-size: 18px;margin: -1px; color:#444444"
                                i18n-matTooltip matTooltip="Go fullscreen" matTooltipPosition="above">open_in_full
                              </mat-icon>
                              <mat-icon *ngIf="isEditFullscreen" style="font-size: 18px;margin: -1px; color:#444444"
                                i18n-matTooltip matTooltip="Close fullscreen" matTooltipPosition="above">
                                close_fullscreen
                              </mat-icon>
                            </button>
                          </span>
                        </div>
                      </quill-editor>
                    </div>
                  </div>
                </div>
                <!-- Update/Cancel Action Buttons and File Attachments-->
                <div class="edit-action-footer" fxLayout="row" fxLayoutAlign="space-between center" style="margin-top: 10px;">
                  <div>
                    <div *ngIf="comment['attachment'].hasOwnProperty('name') && !isEditFileRemoved"
                      class="edit-files-chip-row">
                      <span class="file-chip" fxLayout="row" fxLayoutAlign="space-between center">
                        <span [matTooltip]="comment.attachment.name" matTooltipPosition="above">{{comment.attachment.name | truncate:8 }}</span>
                        <mat-icon (click)="clearEditAttachment(comment, i)">cancel</mat-icon>
                      </span>
                    </div>
                    <div *ngIf="editUploadcheck" class="chip-row">
                      <span class="file-chip" *ngFor="let file of filesEdit let i = index" fxLayout="row"
                        fxLayoutAlign="space-between center">
                        <span>{{file.name | truncate: 8 }}</span>
                        <mat-icon (click)="clearEditAttachment(comment,i)">cancel</mat-icon>
                      </span>
                    </div>
                  </div>
                  <!-- comment edit actions -->
                  <div class="actions" fxLayout="row" fxLayoutGap="10px">
                    <button mat-button (click)="onCancelEdit(comment)">
                      <span i18n>Cancel</span>
                    </button>
                    <button mat-button color="primary" (click)="handler('edit', comment)" 
                        [disabled]="!isCommentChanged ? true : (!comment.edited_text || comment.edited_text == null ) && isAllowAttachment" 
                    >
                      <span i18n>Update</span>
                    </button>
                  </div>
                </div>
              </div>
              <div class="loading-comments" fxLayoutAlign="center center"
                *ngIf="commentsLoading && commentThread.length == 0" fxFlex="100">
                <mat-spinner fxLayoutAlign="center center" diameter="40" strokeWidth="3"></mat-spinner>
              </div>
            </div>
          </div>
        </mat-card>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
