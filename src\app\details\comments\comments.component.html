<div class="wrapper white-bg" fxFlex="100">
  <!-- haeder -->
  <div class="filter-container" fxLayoutAlign="center center">
    <div class="filter-head" fxLayout="row" fxLayoutAlign="space-between center">
      <div fxLayout="row" fxLayoutGap="10px" fxFlex="100" class="search-container" fxLayoutAlign="space-between center">
        <!-- toggle button -->
        <div fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="center center">
          <mat-icon class="go-back" [routerLink]="originFrom"
            [queryParams]="{sub: subscriptionId, row_id: row_id, bucket: bucket}">west</mat-icon>
          <mat-button-toggle-group #group="matButtonToggleGroup" [value]="commentsListForType" class="toggle-btn"
            (change)="onButtonToggle(group.value)">
            <mat-button-toggle value="batch" i18n>Batch Wise</mat-button-toggle>
            <mat-button-toggle value="row" i18n>SKU Wise</mat-button-toggle>
          </mat-button-toggle-group>
        </div>
        <!-- search bar -->
        <div class="comment-header">
          <!-- <p class="text-theme-primary">Comments</p> -->
          <mat-form-field fxFlex="100" appearance="none" class="search-filter">
            <input id="search" matInput i18n-placeholder placeholder="Search by Batch or SKU Id..." [(ngModel)]="search"
              #searchVal name="searchVal" (keyup.enter)="getSearchedItem(search)" />
            <mat-icon matPrefix class="search-icon" (click)="getSearchedItem(search)">search</mat-icon>
            <mat-icon matSuffix class="cancel-search" (click)="resetSearch()" *ngIf="search">close</mat-icon>
          </mat-form-field>
        </div>

      </div>
    </div>
  </div>
  <!-- comments container -->
  <div class="comments-wrapper" *ngIf="commentsList?.length > 0 && !dataLoading">
    <div class="comment-division" fxFlex="100" fxLayout="row" fxLayoutGap="10">
      <!-- short comments (left part) -->
      <div class="division-1" fxFlex="30" infiniteScroll [scrollWindow]="false" [infiniteScrollDistance]="4"
        [infiniteScrollUpDistance]="1.5" [infiniteScrollThrottle]="50" (scrolled)="onScrollDown()">
        <!-- batch/sku wise comment card -->
        <mat-card *ngFor="let comment of commentsList; let i=index" (click)="getActiveCommentIndex(i, comment)"
          [class.active-comment]="activeCommentIndex == i+1">
          <!-- comment title -->
          <div fxLayout="row" class="card-info" fxLayoutGap="5px" fxLayoutAlign="space-between center" fxLayout="row">
            <p class="batch-caption text-theme-primary" fxLayout="row">
              <span>{{ comment.category_id }}</span>
              <span class="batch-caption text-theme-primary" fxLayout="row" [matTooltip]="
            comment.title?.length > 25
              ? comment.title
              : null
            " matTooltipPosition="above">
                ({{comment.title |  truncate: 25}})
              </span>
            </p>

            <!-- <mat-icon *ngIf="commentsListForType !== 'batch'" fxLayout="column" [matTooltip]="comment.batch_id">info_outline</mat-icon> -->
            <!-- <img *ngIf="commentsListForType !== 'batch'" src="../../../assets/images/landing-page/Info-circle.svg"
              [matTooltip]="comment.batch_id" matTooltipPosition="above" /> -->
          </div>

          <!-- comment content -->
          <!-- comment header -->
          <div fxLayout="row" class="profile">
            <img src={{comment.profile_picture}} />
            <div fxLayout="column" class="profile-info">
              <span *ngIf="comment.created_by" class="username">{{ comment.created_by }}</span>
              <span class="comment-time">
                {{ comment.created_at | date: 'y/MM/dd' }}
              </span>
            </div>
          </div>
          <!-- comment body -->
          <div class="comments">
            <p>
              <span class="comment-text side-panel-comment-text"
                [innerHTML]="comment.latest_comment  | comment:comment | plainString">
              </span>
            </p>

          </div>
        </mat-card>
      </div>
      <!-- detailed view (right part) -->
      <div class="division-2" fxFlex="70" *ngIf="!commentsLoading" fxLayout="column">
        <div class="comment-section" fxLayoutAlign="space-between center" fxLayout="row">
          <div fxLayout="row" class="card-info" fxLayoutGap="20">
            <!-- comment thread main title -->
            <div class="comment-header" fxLayout="row" fxLayoutGap="5px">
              <p class="batch-caption text-theme-primary">
                {{ selectedCommentId }}
              </p>
              <p class="text-theme-primary" [matTooltip]="
            commentThreadTitle?.length > 50
              ? commentThreadTitle
              : null
            " matTooltipPosition="above">
                ({{commentThreadTitle |  truncate: 50}})
              </p>
            </div>
          </div>
          <!-- header buttons -->
          <div class="action-btn">
            <!-- <button mat-button class="mention-btn stroked-btn-without-border">Only @Mentions</button> -->
            <!-- <button mat-button class="notify-btn stroked-btn-without-border">Notify Always</button> -->
            <button mat-button class="resolve-btn" (click)="resolveComment()" *ngIf="!commentIsResolved" i18n>Mark as
              Resolved</button>
          </div>
        </div>
        <!-- comment thread scroll container -->
        <div class="comments-scroll-wrapper">
          <div class="no-comments-yet" fxLayout="column" fxLayoutAlign="center center"
            *ngIf="commentThread?.length == 0 && !commentsLoading">
            <span class="main-msg" i18n>No Comments yet.</span>
            <span class="sub-msg" i18n>To create a new comment, use @username</span>
          </div>
          <!-- comment thread scroll container -->
          <div class="comments-scroll" #scrollContainer fxLayout="column" fxLayoutGap="15px" infiniteScroll
            [scrollWindow]="false" [infiniteScrollDistance]="1" [infiniteScrollUpDistance]="1"
            [infiniteScrollThrottle]="50" (scrolled)="onCommentThreadScroll()" *ngIf="commentThread?.length > 0">
            <div fxLayout="row" class="cmt-profile" *ngFor="let comment of commentThread; let i = index">
              <!-- comment profile picture -->
              <img class="img" src="{{
              comment.profile_picture
            }}" />
              <div fxLayout="column" class="cmt-profile-picture">
                <!-- comment header -->
                <!-- <p class="username">{{ comment.created_by }}</p> -->
                <div fxLayout="row" fxLayoutGap="10px" style="align-items: center">
                  <span class="username" *ngIf="comment.created_by">{{ comment.created_by }}</span>
                  <span class="comment-time">
                    {{ comment.created_at | date: 'y/MM/dd' }}
                  </span>
                </div>

                <!-- comment content -->
                <div class="comments" [class.hover-color]="row === i" *ngIf="!comment.editable"
                  fxLayoutAlign="space-between start" fxFlex="100" (mouseover)="
                userData.username == comment.created_by_username
                  ? (row = i)
                  : (row = -1)
              " (mouseleave)="row = -1">
                  <span fxFlex="90">
                    <div class="comment-text">
                      <span [innerHTML]="comment.text | innerHTMLstyles"></span>
                    </div>
                    <!-- <span class="comment-text">{{comment.attachment.name}}</span> -->
                    <div fxLayout="row" *ngIf="comment.attachment.name" class="file-chip-row">
                      <a [href]="comment.attachment.signed_url" [matTooltip]="comment.attachment.name"
                        matTooltipPosition="above">
                        <span class="file-chip">{{comment.attachment.name | truncate:8}}
                        </span>
                      </a>
                    </div>
                  </span>
                  <!-- <span class="tagged-users text-theme-primary"
                      *ngFor="let user of comment.tagged_users">@{{ user.name }}</span> -->


                  <span style="padding-left: 20px;" fxFlex="10" fxLayoutGap="10px" *ngIf="
                  userData.username == comment.created_by_username && row == i
                ">
                    <img (click)="editComment(comment); comment.editable = true"
                      src="../../../assets/images/comments-page/edit.svg" i18n-matTooltip matTooltip="Edit"
                      matTooltipPosition="above" />
                    <img class="delete-icon" (click)="deleteComment(comment.comment_id, i)"
                      src="../../../assets/images/comments-page/trash.svg" i18n-matTooltip matTooltip="Delete"
                      matTooltipPosition="above" />
                  </span>
                </div>
                <!-- edit comment part -->
                <div class="edit-comment-box" fxLayout="column" *ngIf="comment.editable" fxLayoutAlign="space-between">
                  <!------------- editor ----------------->
                  <div [ngClass]="{'editor-fs':isEditFullscreen}" #fullScreenEdit>
                    <quill-editor [placeholder]=editorPlaceholder [styles]="editorStyle" [modules]="editModuleconfig"
                      [(ngModel)]="comment.edited_text" theme="snow" (onFocus)="onEditorFocus($event)"
                      (onBlur)="onEditorBlur($event)" (onContentChanged)="onContentChange($event, 'edit', comment)"
                      (onEditorCreated)="getEditorInstance($event,'edit')" customToolbarPosition="bottom">
                      <div quill-editor-toolbar>
                        <span class="ql-formats">
                          <button class="ql-bold" i18n-matTooltip matTooltip="Bold" matTooltipPosition="above"></button>
                          <button class="ql-italic" i18n-matTooltip matTooltip="Italic"
                            matTooltipPosition="above"></button>
                          <button class="ql-underline" i18n-matTooltip matTooltip="Underline"
                            matTooltipPosition="above"></button>
                          <button class="ql-strike" i18n-matTooltip matTooltip="Strikethrough"
                            matTooltipPosition="above"></button>
                          <button class="ql-blockquote" i18n-matTooltip matTooltip="Quote"
                            matTooltipPosition="above"></button>
                          <button class="ql-code" i18n-matTooltip matTooltip="Insert code"
                            matTooltipPosition="above"></button>
                          <select class="ql-color" title="Text colour" i18n-title matTooltipPosition="above"></select>
                          <button class="ql-header" value="1" i18n-matTooltip matTooltip="Heading 1"
                            matTooltipPosition="above"></button>
                          <button class="ql-header" value="2" i18n-matTooltip matTooltip="Heading 2"
                            matTooltipPosition="above"></button>
                          <button class="ql-list" value="ordered" i18n-matTooltip matTooltip="Numbered list"
                            matTooltipPosition="above"></button>
                          <button class="ql-list" value="bullet" i18n-matTooltip matTooltip="Bullet list"
                            matTooltipPosition="above"></button>
                          <button class="ql-link" i18n-matTooltip matTooltip="Insert link"
                            matTooltipPosition="above"></button>
                          <!-- <button class="ql-image" i18n-matTooltip matTooltip="Insert image"
                            matTooltipPosition="above"></button> -->
                          <input style="display: none" type="file" accept="image/*"
                            (change)="insertImage($event, 'edit')" #fileInputEdit>
                          <button (click)="fileInputEdit.click()" *ngIf="!isImgUploadEdit">
                            <mat-icon style="font-size: 18px;margin: -1px; color:#444444; padding-top: 2px;"
                              i18n-matTooltip matTooltip="Insert image" matTooltipPosition="above">collections
                            </mat-icon>
                          </button>
                          <button *ngIf="isImgUploadEdit">
                            <mat-progress-spinner color="primary" mode="indeterminate" diameter="20">
                            </mat-progress-spinner>
                          </button>
                          <button class="ql-attachment" i18n-matTooltip
                            *ngIf="isAllowAttachment && filesEdit?.length == 0" matTooltip="Attach a file"
                            matTooltipPosition="above">
                            <label for="inputTag2">
                              <mat-icon style="font-size: 22px;margin: -1px; color:#444444 !important;"
                                class="attach-file">attachment</mat-icon>
                              <input id="inputTag2" type="file" style="display: none;"
                                (change)="onFileSelected($event, 'edit')" />
                            </label>
                          </button>
                          <button (click)="toggleFullscreen(!isEditFullscreen, 'edit')" class="ql-fullscreen">
                            <mat-icon *ngIf="!isEditFullscreen" style="font-size: 18px;margin: -1px; color:#444444"
                              i18n-matTooltip matTooltip="Go fullscreen" matTooltipPosition="above">open_in_full
                            </mat-icon>
                            <mat-icon *ngIf="isEditFullscreen" style="font-size: 18px;margin: -1px; color:#444444"
                              i18n-matTooltip matTooltip="Close fullscreen" matTooltipPosition="above">close_fullscreen
                            </mat-icon>
                          </button>
                        </span>
                      </div>
                    </quill-editor>
                  </div>

                  <div class="box-footer" fxLayout="row" style="margin-top: 10px;">
                    <div *ngIf="comment['attachment'].hasOwnProperty('name') && !isEditFileRemoved"
                      class="file-chip-row">
                      <span class="file-chip" fxLayout="row" fxLayoutAlign="space-between center">
                        <span>{{comment.attachment.name}}</span>
                        <mat-icon (click)="clearEditAttachment(comment, i)">cancel</mat-icon>
                      </span>
                    </div>
                    <div class="file-chip-row">
                      <ng-container *ngIf="editUploadcheck">
                        <span class="file-chip" *ngFor="let file of filesEdit let i = index" fxLayout="row"
                          fxLayoutAlign="space-between center">
                          <span>{{file.name}}</span>
                          <mat-icon (click)="clearEditAttachment(comment,i)">cancel</mat-icon>
                        </span>
                      </ng-container>
                    </div>
                    <div class="actions" fxLayout="row" fxLayoutGap="10px">
                      <button mat-stroked-button (click)="onCancelEdit(comment)" i18n>
                        Cancel
                      </button>
                      <button mat-flat-button color="primary" (click)="handler('edit', comment)"
                        [disabled]="!isCommentChanged ? true :  (editedContentWithoutTaggedUsers.length == 0 || !comment.edited_text || comment.edited_text == null ) && isAllowAttachment"
                        i18n>
                        Update
                      </button>
                    </div>

                  </div>
                  <!------------- editor ----------------->
                </div>
              </div>
            </div>
          </div>
          <!-- comment area -->
          <div class="comment-box">
            <!------------- editor ----------------->
            <div [ngClass]="{'editor-fs':isFullscreen}" #fullScreen>
              <quill-editor [placeholder]=editorPlaceholder [styles]="editorStyle" [modules]="modules"
                [(ngModel)]="content" theme="snow" (onFocus)="onEditorFocus($event)" (onBlur)="onEditorBlur($event)"
                (onContentChanged)="onContentChange($event, 'create')" (onEditorCreated)="getEditorInstance($event)"
                customToolbarPosition="bottom">
                <div quill-editor-toolbar>
                  <span class="ql-formats">
                    <button class="ql-bold" i18n-matTooltip matTooltip="Bold" matTooltipPosition="above"></button>
                    <button class="ql-italic" i18n-matTooltip matTooltip="Italic" matTooltipPosition="above"></button>
                    <button class="ql-underline" i18n-matTooltip matTooltip="Underline"
                      matTooltipPosition="above"></button>
                    <button class="ql-strike" i18n-matTooltip matTooltip="Strikethrough"
                      matTooltipPosition="above"></button>
                    <button class="ql-blockquote" i18n-matTooltip matTooltip="Quote"
                      matTooltipPosition="above"></button>
                    <button class="ql-code" i18n-matTooltip matTooltip="Insert code"
                      matTooltipPosition="above"></button>
                    <select class="ql-color" title="Text colour" i18n-title></select>
                    <button class="ql-header" value="1" i18n-matTooltip matTooltip="Heading 1"
                      matTooltipPosition="above"></button>
                    <button class="ql-header" value="2" i18n-matTooltip matTooltip="Heading 2"
                      matTooltipPosition="above"></button>
                    <button class="ql-list" value="ordered" i18n-matTooltip matTooltip="Numbered list"
                      matTooltipPosition="above"></button>
                    <button class="ql-list" value="bullet" i18n-matTooltip matTooltip="Bullet list"
                      matTooltipPosition="above"></button>
                    <button class="ql-link" i18n-matTooltip matTooltip="Insert link"
                      matTooltipPosition="above"></button>
                    <!-- <button class="ql-image" i18n-matTooltip matTooltip="Insert image" matTooltipPosition="above"></button> -->
                    <input style="display: none" type="file" accept="image/*" (change)="insertImage($event)" #fileInput>
                    <button (click)="fileInput.click()" *ngIf="!isImgUpload">
                      <mat-icon style="font-size: 18px;margin: -1px; color:#444444; padding-top: 2px;" i18n-matTooltip
                        matTooltip="Insert image" matTooltipPosition="above">collections</mat-icon>
                    </button>
                    <button *ngIf="isImgUpload">
                      <mat-progress-spinner color="primary" mode="indeterminate" diameter="20">
                      </mat-progress-spinner>
                    </button>
                    <button class="ql-attachment" *ngIf="files.length == 0" i18n-matTooltip matTooltip="Attach a file"
                      matTooltipPosition="above">
                      <label for="inputTag">
                        <mat-icon style="font-size: 22px;margin: -1px; color:#444444 !important;" class="attach-file">
                          attachment</mat-icon>
                        <input id="inputTag" type="file" style="display: none;"
                          (change)="onFileSelected($event, 'create')" />
                      </label>

                    </button>
                    <button (click)="toggleFullscreen(!isFullscreen, 'create')" class="ql-fullscreen">
                      <mat-icon *ngIf="!isFullscreen" style="font-size: 18px;margin: -1px; color:#444444"
                        i18n-matTooltip matTooltip="Go fullscreen" matTooltipPosition="above">open_in_full</mat-icon>
                      <mat-icon *ngIf="isFullscreen" style="font-size: 18px;margin: -1px; color:#444444" i18n-matTooltip
                        matTooltip="Close fullscreen" matTooltipPosition="above">close_fullscreen</mat-icon>
                    </button>
                  </span>
                </div>
              </quill-editor>
            </div>
            <!-- <div fxLayout="row" *ngIf="uploadCheck" class="file-chip-row">
                  <p class="file-chip"  *ngFor="let file of files let i = index">{{file.name}}
                    <span><mat-icon  (click)="clear(i)">cancel</mat-icon></span>
                  </p>
                </div> -->

            <div class="box-footer" fxLayout="row" fxLayoutAlign="space-between center" style="margin-top: 10px;">
              <!-- <mat-icon class="text-theme-primary attach-file">attachment</mat-icon> -->
              <div class="file-chip-row">
                <ng-container *ngIf="uploadCheck">
                  <div class="file-chip" *ngFor="let file of files let i = index" fxLayout="row"
                    fxLayoutAlign="space-between center">
                    <span>{{file.name}}</span>
                    <mat-icon (click)="clear(i)">cancel</mat-icon>
                  </div>
                </ng-container>
              </div>
              <button mat-raised-button class="filled-btn-primary" (click)="handler('create')"
                [disabled]="(content == '' || content == null || contentWithoutTaggedUsers.length == 0) && files.length <= 0"
                i18n>
                Send
              </button>
            </div>
            <!------------- editor ----------------->

          </div>
        </div>

      </div>
      <!-- division 2/comment thread loading spinner -->
      <div class="loading-spinner center-placement" fxLayoutAlign="center center" *ngIf="commentsLoading">
        <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
      </div>
    </div>
  </div>

  <!-- progress spinner for page -->
  <div class="loading-spinner center-placement" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
  </div>

  <!-- no data section -->
  <div class="no-data" *ngIf="commentsList?.length == 0 && !dataLoading" fxLayout="row" fxLayoutGap="10px">
    <!-- <mat-icon fontSet="material-icons-outlined">info</mat-icon> -->
    <span i18n>No comments to display.</span>
  </div>
</div>
