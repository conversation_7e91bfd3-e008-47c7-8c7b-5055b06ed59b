import {
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ReviewService } from '../../services/review.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { ENTER, COMMA } from '@angular/cdk/keycodes';
import { HttpErrorResponse } from '@angular/common/http';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatSnackBar } from '@angular/material/snack-bar';
import moment from 'moment';
import { CommentsService } from 'src/app/services/comments.service';
import { ImageViewerDialogComponent } from 'src/app/_dialogs/image-viewer-dialog/image-viewer-dialog.component';
import { ReferenceUrlDialogComponent } from 'src/app/_dialogs/reference-url-dialog/reference-url-dialog.component';
import { UndoSnackbarComponent } from 'src/app/_dialogs/undo-snackbar/undo-snackbar.component';
import { UserService } from '../../services/user.service';
import { ProductsService } from '../../services/products.service';
import { QuillEditorComponent } from 'ngx-quill';
import { DOCUMENT } from '@angular/common';
import { FileUploadService } from 'src/app/services/file-upload.service';
import { SnackbarService } from '../../services/snackbar.service';
import { ConfirmDialog } from 'src/app/_dialogs/confirm-dialog/confirm-dialog.component';

export interface Fruit {
  name: string;
}
interface User {
  name: string;
  username: string;
}

@Component({
  selector: 'app-review-mode',
  templateUrl: './review-mode.component.html',
  styleUrls: ['./review-mode.component.scss'],
})
export class ReviewModeComponent implements OnInit {
  uploadCheck: boolean;
  editUploadcheck: boolean = false;
  isEditFileRemoved: boolean = false;
  attachmentID: number;
  dialogRef: MatDialogRef<ConfirmDialog, any>;
  uploadedFile: any;
  commentContentChanged: boolean = false;
  @HostListener('document:fullscreenchange', ['$event'])
  @HostListener('document:webkitfullscreenchange', ['$event'])
  @HostListener('document:mozfullscreenchange', ['$event'])
  @HostListener('document:MSFullscreenChange', ['$event'])
  fullScreenChange() {
    this.isFullscreen = !this.isFullscreen;
    this.isEditFullscreen = !this.isEditFullscreen;
  }
  @ViewChild(QuillEditorComponent) editor: QuillEditorComponent;
  @ViewChild('fullScreen') divRef: ElementRef;
  @ViewChild('fullScreenEdit') editFullScreen: ElementRef;
  @ViewChild('fileInput') imgFileInput;
  @ViewChild('fileInputEdit') imgFileInputEdit;

  /** editor ngx quill */

  content = '';
  matContent = '';
  selectedUsers: any[] = [];
  selectedUsersEdited: any[] = [];
  editorRef;
  editorRefEdit;
  editorStyle = {
    maxHeight: '150px',
    height: '80px',
  };

  // create comment editor config
  modules = {
    mention: {
      allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
      onSelect: (item, insertItem) => {
        const editor = this.editor.quillEditor;
        insertItem(item);
        // necessary because quill-mention triggers changes as 'api' instead of 'user'
        editor.insertText(editor.getLength() - 1, '', 'user');
      },
      source: (searchTerm, renderList) => {
        const values = this.mentionUsers;
        if (searchTerm.length === 0) {
          renderList(values, searchTerm);
        } else {
          const matches = [];

          values.forEach((entry) => {
            if (
              entry.value.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1
            ) {
              matches.push(entry);
            }
          });
          renderList(matches, searchTerm);
        }
      },
    },
  };
  // edit comment editor config
  editModuleconfig = {
    mention: {
      allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
      onSelect: (item, insertItem) => {
        const editor = this.editor.quillEditor;
        insertItem(item);
        // necessary because quill-mention triggers changes as 'api' instead of 'user'
        editor.insertText(editor.getLength() - 1, '', 'user');
      },
      source: (searchTerm, renderList) => {
        const values = this.mentionUsers;

        if (searchTerm.length === 0) {
          renderList(values, searchTerm);
        } else {
          const matches = [];

          values.forEach((entry) => {
            if (
              entry.value.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1
            ) {
              matches.push(entry);
            }
          });
          renderList(matches, searchTerm);
        }
      },
    },
  };
  isFullscreen: boolean = false;
  isEditFullscreen: boolean = false;
  isAllowAttachment: boolean = false;
  isImgUpload: boolean = false;
  isImgUploadEdit: boolean = false;
  files = [];
  currentInput;
  visible = true;
  selectable = true;
  removable = true;
  addOnBlur = true;
  readonly separatorKeysCodes: number[] = [ENTER, COMMA];
  fruits: Fruit[] = [];
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: string;
  customStartDate: string;
  maxDate: Date;
  minDate: Date;
  pageNumber;
  selected = 'recent';
  start_date;
  end_date;
  page: number;
  size: number;
  selectedProducts = {};
  subscriptionId: any;
  filterList: any;
  searchedMultipleVals: string[] = [];
  filterObj: Object = {};
  datePickerValue;
  searchedItems: string[] = [];
  reviewPageFilters;
  batch_id;
  search: string;
  dataLoading;
  reviewList;
  previousProductId;
  nextProductId;
  reviewData: any[] = [];
  currentItem;
  totalItems;
  rowId;
  predictionId;
  predictionName;
  // comments
  activeCommentIndex = 1;
  comments: any[] = [];
  text = ``;
  loading = false;
  choices: User[] = [];
  mentionUsers: any[];
  taggedUsersByUsername: any[] = [];
  taggedUsersByName: any[] = [];
  taggedUsers: any[] = [];
  defSuggestionDisplayCount: number = 3;
  commentThread: any[] = [];
  threadPage: number = 1;
  threadSize: number = 10;
  threadTotalItems: number;
  threadTotalPage: number;
  commentsLoading: boolean;
  commentsListForType = 'row';
  userData;
  searchInComment = '';
  nextid;
  selectedFilters: string[] = [];
  productId;
  diableBtn;
  diableNextBtn;
  row_id;
  bucket;
  permissionsObject;
  actionPermission;
  fromPage;
  editorPlaceholder = $localize`Comment or mention others with @`;
  filesEdit = [];
  row;
  moveToBucketList: [];
  bucketVal;
  undoBucketValue;
  isCommentChanged: boolean = false;
  productModeChecked: boolean = false;
  constructor(
    @Inject(DOCUMENT) private document: any,
    private reviewService: ReviewService,
    public dialog: MatDialog,
    private router: Router,
    private activatedroute: ActivatedRoute,
    public matSnackbar: MatSnackBar,
    private commentsService: CommentsService,
    private userService: UserService,
    private productService: ProductsService,
    private fileUploadService: FileUploadService,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit() {
    // retrieve subscription id
    this.activatedroute.queryParams.subscribe((params: Params) => {
      this.row_id = params.row_id;
      this.bucket = params.bucket;
      this.fromPage = params.from;
    });
    // add subscription id from local storage in url as param
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.getReviewFilterList(this.subscriptionId);
    this.currentDate = moment().format('YYYY-MM-DD');
    const currentYear = new Date().getFullYear();
    this.minDate = new Date(currentYear - 20, 0, 1);
    this.maxDate = new Date();
    // get user data from local storage
    if (localStorage.getItem('user')) {
      this.userData = JSON.parse(localStorage.getItem('user'));
    }
    // retaining bucket for undo
    this.undoBucketValue = this.activatedroute.snapshot.queryParams['bucket'];
    this.actionPermission = this.userService.appPermissions.bucket_permissions;

    // get all previous filters from local storage only if current module is same as module in localstorage item reviewModeFilterObj
    if (localStorage.getItem('reviewModeFilterObj'))
      this.reviewPageFilters = JSON.parse(
        localStorage.getItem('reviewModeFilterObj')
      );
    if (
      this.reviewPageFilters &&
      this.reviewPageFilters != 'undefined' &&
      JSON.parse(localStorage.reviewModeFilterObj).hasOwnProperty(
        this.subscriptionId
      )
    ) {
      // retrieve all filter values
      this.reviewPageFilters[this.subscriptionId].q.forEach((element) => {
        this.searchedItems.push(element);
      });
      this.datePickerValue = this.reviewPageFilters[this.subscriptionId]
        ?.date_picker_value
        ? this.reviewPageFilters[this.subscriptionId].date_picker_value
        : 'recent';
      this.selected = this.datePickerValue;
      this.selectedProducts = this.reviewPageFilters[this.subscriptionId]
        ?.product_list_model
        ? this.reviewPageFilters[this.subscriptionId].product_list_model
        : {};
      this.start_date = this.reviewPageFilters[this.subscriptionId]?.start_date
        ? this.reviewPageFilters[this.subscriptionId]?.start_date
        : '';
      this.end_date = this.reviewPageFilters[this.subscriptionId]?.end_date
        ? this.reviewPageFilters[this.subscriptionId]?.end_date
        : '';
      this.searchedMultipleVals = this.reviewPageFilters[this.subscriptionId]
        ?.product_list
        ? this.reviewPageFilters[this.subscriptionId].product_list
        : [];
    } else {
      this.start_date = '';
      this.end_date = '';
      this.searchedMultipleVals = [];
      this.searchedItems = [];
    }

    // push batch Id from URL in searched items array
    // ignore whitespaces
    if (
      this.batch_id &&
      this.batch_id != '' &&
      this.searchedItems.indexOf(this.batch_id.trim()) < 0
    ) {
      this.searchedItems.push(this.batch_id.trim());
    }
    // create an object to be saved in local storage
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      date_picker_value: this.datePickerValue,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
    };
    localStorage.setItem('reviewModeFilterObj', JSON.stringify(this.filterObj));

    this.datePickerOptions = [
      {
        display: 'Recent',
        value: 'recent',
        start_date: '',
      },
      {
        display: $localize`Last Week`,
        value: 'last_week',
        start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
      },
      {
        display: $localize`Last Month`,
        value: 'last_month',
        start_date: moment()
          .subtract(1, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: $localize`Last Quarter`,
        value: 'last_quarter',
        start_date: moment()
          .subtract(3, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        value: 'custom_range',
      },
    ];
    // get review product details
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date,
      this.row_id,
      this.bucket
    );
    // get user name List
    this.getUserNameList();
  }

  /**
   * Toggle All settings
   * @param e
   */
  toggleSwitches = (e) => {
    if (e.checked) {
      this.activatedroute.queryParams.subscribe((params: Params) => {
        this.row_id = params.row_id;
        this.bucket = params.bucket;
      });
      // this.productModeChecked = true;
      this.router.navigate(['/details/product-details'], {
        queryParams: {
          sub: this.subscriptionId,
          row_id: this.row_id,
          from: this.fromPage,
          bucket: this.bucket,
        },
      });
    } else {
      this.productModeChecked = false;
    }
    this.setUserPreferenceInCookie(e.checked, 30);
  };

  /**
   * set user product details view mode preference
   * @param value
   * @param days
   */
  setUserPreferenceInCookie = (value, days) => {
    let view = value == true ? 'product-details' : 'review-mode';
    var expires = '';
    if (days) {
      var date = new Date();
      date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
      expires = '; expires=' + date.toUTCString();
    }
    this.document.cookie =
      'view_mode' + '=' + (view || '') + expires + '; path=/';
  };

  /**
   * get Date
   * @param range
   * @returns
   */
  getDataForDate = (range: string) => {
    // do nothing for custom range
    if (this.selected == 'custom_range') {
      return null;
    }
    (this.customStartDate = ''), (this.customEndDate = '');
    // for others
    let interval = this.datePickerOptions.filter((item) => {
      return item.value === range;
    })[0];
    this.start_date = interval['start_date'];
    if (this.selected == 'recent') {
      this.end_date = '';
    } else {
      this.end_date = this.currentDate;
    }
    this.page = 1;
    this.size = 5;
    // retain datepicker dropdown value for auto selection
    this.datePickerValue = range;
    // update reviewModeFilterObj in localstorage
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list: this.searchedMultipleVals,
      product_list_model: this.selectedProducts,
      date_picker_value: this.datePickerValue,
    };
    localStorage.setItem('reviewModeFilterObj', JSON.stringify(this.filterObj));
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date,
      null,
      this.bucket
    );
  };

  sd;
  ed;
  /**
   * set date range
   * @param dateRangeStart
   * @param dateRangeEnd
   */
  dateRangeChange = (
    dateRangeStart: HTMLInputElement,
    dateRangeEnd: HTMLInputElement
  ) => {
    if (
      moment(dateRangeStart?.value, 'DD-MM-YYYY').isValid() &&
      moment(dateRangeEnd?.value, 'DD-MM-YYYY').isValid()
    ) {
      this.selected = 'custom_range';
      this.sd = moment(dateRangeStart?.value, 'DD-MM-YYYY').format(
        'YYYY-MM-DD'
      );
      this.ed = moment(dateRangeEnd?.value, 'DD-MM-YYYY').format('YYYY-MM-DD');
      this.page = 1;
      this.size = 5;
      if (this.selected == 'custom_range') {
        this.start_date = this.sd;
        this.end_date = this.ed;
      }
    }
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date,
      null,
      this.bucket
    );
  };
  /**
   * search chip
   * @param event
   */
  addQuery(event: MatChipInputEvent): void {
    const value = event.value;
    if (value && value != '' && this.searchedItems.indexOf(value.trim()) < 0) {
      this.searchedItems.push(value.trim());
      this.filterObj[this.subscriptionId] = {
        q: this.searchedItems,
        start_date: this.start_date,
        end_date: this.end_date,
        product_list_model: this.selectedProducts,
        product_list: this.searchedMultipleVals,
        date_picker_value: this.datePickerValue,
      };
      localStorage.setItem(
        'reviewModeFilterObj',
        JSON.stringify(this.filterObj)
      );
      this.getReviewProductDetail(
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date,
        null,
        this.bucket
      );
      this.search = '';
    }
  }

  /**
   * Removes search query from filter list
   * @param item
   */
  removeQuery = (item) => {
    const index = this.searchedItems.indexOf(item.trim());
    this.searchedItems.splice(index, 1);
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
    };
    localStorage.setItem('reviewModeFilterObj', JSON.stringify(this.filterObj));
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date,
      null,
      this.bucket
    );
  };

  /**
   * reset filter
   */
  resetFilters = () => {
    this.search = '';
    this.selected = 'recent';
    this.datePickerValue = 'recent';
    this.searchedItems = [];
    this.searchedMultipleVals = [];
    this.filterObj = {};
    this.start_date = '';
    this.end_date = '';
    this.selectedProducts = {};
    this.router.navigate([], {
      relativeTo: this.activatedroute,
      queryParams: {
        sub: this.subscriptionId,
        row_id: this.row_id,
        bucket: this.bucket,
      },
    });

    localStorage.removeItem('reviewModeFilterObj');
    this.customStartDate = '';
    this.customEndDate = '';
    this.getReviewFilterList(this.subscriptionId);
    this.filterList = [];
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date,
      this.row_id,
      this.bucket
    );
  };

  resetSearch = () => {
    this.search = '';
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date,
      this.row_id,
      this.bucket
    );
  };

  /**
   * get filters on selection
   */
  getProductSelection = () => {
    this.searchedMultipleVals = [];
    this.selectedProducts;
    for (const property in this.selectedProducts) {
      if (this.selectedProducts[property].length) {
        this.selectedProducts[property].forEach((element) => {
          this.searchedMultipleVals.push(element);
        });
      }
    }
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
    };
    localStorage.setItem('reviewModeFilterObj', JSON.stringify(this.filterObj));
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date,
      null,
      this.bucket
    );
  };

  /**
   * get options for filters
   * @param subscription_id
   */
  getReviewFilterList = (subscription_id) => {
    this.reviewService.getReviewFilterList(subscription_id, true).subscribe({
      next: (resp) => {
        this.filterList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
  };

  /**
   * get row index id
   * @param subscription_id
   * @param row_index
   */
  getRowIndex = (subscription_id, row_index) => {
    this.reviewService.getRowIndex(subscription_id, row_index).subscribe({
      next: (resp) => {},
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
      },
    });
  };

  openImageSlider = (productImg) => {
    this.dialog.open(ImageViewerDialogComponent, {
      data: { productImg },
    });
  };
  beforeUndoBucket;
  /**
   * get review product data
   * @param search
   * @param filter
   * @param start_date
   * @param end_date
   */

  getReviewProductDetail = (
    search,
    filter,
    start_date,
    end_date,
    row_id,
    bucket
  ) => {
    this.dataLoading = true;
    this.reviewService
      .getReviewModeProductDetail(
        search,
        filter,
        start_date,
        end_date,
        this.subscriptionId,
        row_id,
        bucket
      )
      .subscribe({
        next: (resp) => {
          this.dataLoading = false;
          // this.isDataPresent = resp.result.length === 0 ? true : false;
          this.reviewData = [];
          this.reviewList = resp.result.row_data;
          this.reviewList &&
            Object.keys(this.reviewList).length > 0 &&
            this.reviewData.push(this.reviewList);
          this.row_id = this.reviewList.row_id;
          // update url with the latest row id
          let urlTree = this.router.parseUrl(this.router.url);
          urlTree.queryParams['row_id'] = this.row_id;
          this.router.navigateByUrl(urlTree);
          this.previousProductId = resp.result.previous;
          this.nextProductId = resp.result.next;
          // Current and Total items
          this.currentItem = resp.result.current_index;
          this.totalItems = resp.result.total_rows;
          this.reviewData.forEach((data) => {
            let count = 0;
            data.main_attribute_count = count;
            // sort so that all items wit main_attribute true comes first
            data.predictions.sort(function (a, b) {
              if (b.main_attribute == true) {
                count++;
              }
              data.main_attribute_count = count;
              return b.main_attribute - a.main_attribute;
            });
            data.activeAttribute = data?.predictions[0]?.display_name;
          });
          this.bucketVal = bucket.toLowerCase();
          this.predictionId = resp.result[0]?.predictions[0].prediction_id;
          this.predictionName = resp.result[0]?.predictions[0].display_name;
          this.moveToBucketList = this.actionPermission[this.bucketVal];
          this.rowId = resp.result.row_data.row_id;
          if (this.rowId) {
            this.getCommentThread(
              this.subscriptionId,
              this.threadPage,
              this.threadSize,
              this.commentsListForType,
              this.rowId,
              this.searchInComment,
              true
            );
          }
          return this.reviewData;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * Reference Dialog
   * @param value
   */
  openDialogReferenceURL = (value) => {
    this.dialog.open(ReferenceUrlDialogComponent, {
      data: {
        value: value,
      },
    });
  };

  /**
   * View more btn
   * @param index, event
   */
  viewMore = (e, i) => {
    var dots = this.document.getElementById('dots-' + i);
    var moreText = this.document.getElementById('more-' + i);
    var btnText = this.document.getElementById('moreBtn-' + i);

    if (dots.style.display === 'none') {
      dots.style.display = 'inline';
      btnText.innerHTML = $localize`View More`;
      moreText.style.display = 'none';
    } else {
      dots.style.display = 'none';
      btnText.innerHTML = $localize`View less`;
      moreText.style.display = 'inline';
    }
  };

  /**
   * Accept/Reject suggestion
   * @param item
   * @param prediction
   * @param row_id
   * @param suggestion
   * @param val
   * @param is_accepted
   */
  updateSuggestion = (
    item,
    prediction,
    row_id,
    suggestion,
    val,
    is_accepted
  ) => {
    this.dataLoading = true;
    this.nextid = row_id;
    this.reviewService
      .suggestionAccceptDecilne(
        this.subscriptionId,
        row_id,
        prediction.prediction_id,
        suggestion,
        is_accepted
      )
      .subscribe({
        next: (resp) => {
          this.dataLoading = false;
          // if main attribute is rejected, sku directly goes to rework
          // so go to next sku
          if (val == 'reject' && prediction.main_attribute == true) {
            this.nextid = this.nextProductId;
            // Undo snackbar
            let snackBarRef = this.matSnackbar.openFromComponent(
              UndoSnackbarComponent,
              {
                duration: 30000,
                // passing args as data
                data: {
                  response: resp.detail,
                  module: this.subscriptionId,
                  Id: row_id,
                  onUndo: () => {
                    this.getRowData(
                      this.searchedItems,
                      this.selectedFilters,
                      this.start_date,
                      this.end_date,
                      this.subscriptionId,
                      row_id
                    );
                  },
                },
              }
            );
            // call next sku
            this.getRowData(
              this.searchedItems,
              this.searchedMultipleVals,
              this.start_date,
              this.end_date,
              this.subscriptionId,
              this.nextid
            );
          } else {
            // call same sku
            this.getRowData(
              this.searchedItems,
              this.searchedMultipleVals,
              this.start_date,
              this.end_date,
              this.subscriptionId,
              row_id
            );
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          // 400 means the value was edited in product detail page
          // so get updated values
          if (HttpResponse.status === 400) {
            this.reviewService
              .updateSuggestions(this.subscriptionId, row_id)
              .subscribe({
                next: (res) => {
                  Object.assign(item, res[0]);
                  item.activeAttribute = res[0]?.predictions[0].display_name;
                },
              });
          }
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * post comment
   * @param comment comment
   * @param i index
   */
  postComment = (comment, user, file, i?) => {
    let users = [];
    let IDs = [];
    let obj = {
      comment: comment,
      tagged_users: user,
      attachment_name: file.length > 0 ? file[0].name : file,
      file_hash_ids: IDs,
    };
    // get the editor content as json
    const { ops } = this.editor.quillEditor.getContents();
    // to find all mentions and images
    const result = ops.reduce((acc, { insert }) => {
      if (typeof insert === 'string') {
      } else {
        if (insert.hasOwnProperty('mention')) {
          users.push(insert.mention.id);
        }
        if (insert.hasOwnProperty('image')) {
          const test = insert.image.match(/attachments\/(.*)\//);
          IDs.push(test[1]);
        }
      }
      return acc;
    }, '');
    // remove duplicate tagged users [allowed in editor]
    obj.tagged_users = [...new Set(users)];
    // post comment
    this.commentsService
      .postComment(
        this.subscriptionId,
        this.commentsListForType,
        this.rowId,
        obj
      )
      .subscribe({
        next: (resp) => {
          this.files = [];
          this.selectedUsers = [];
          this.content = '';
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          if (resp['signed_url'] === '') {
            this.getCommentThread(
              this.subscriptionId,
              (this.threadPage = 1),
              this.threadSize,
              this.commentsListForType,
              this.rowId,
              this.searchInComment,
              true
            );
            return;
          }
          this.fileUploadService
            .getSessionURI(resp.signed_url, file[0])
            .subscribe({
              next: (progress) => {
                this.getCommentThread(
                  this.subscriptionId,
                  (this.threadPage = 1),
                  this.threadSize,
                  this.commentsListForType,
                  this.rowId,
                  this.searchInComment,
                  true
                );
                this.fileUploadService
                  .uploadCompelte(true, resp.comment_id, this.subscriptionId)
                  .subscribe({
                    next: (resp) => {},
                  });
              },
              error: (HttpResponse: HttpErrorResponse) => {
                this.dataLoading = false;
                this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
              },
            });
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * get user list for tagging in comments
   */
  getUserNameList = () => {
    this.commentsService.getUserNamesToTag(this.subscriptionId).subscribe({
      next: (resp) => {
        this.mentionUsers = resp.result.map((obj) => ({
          id: obj.username,
          value: obj.name,
        }));
      },
    });
  };

  /**
   * get comment thread for respective sku/batch comment
   * @param subs_id
   * @param page
   * @param size
   * @param category
   * @param batch_or_row_id
   * @param q
   * @param comment_thread
   */
  getCommentThread = (
    subs_id,
    page,
    size,
    category,
    batch_or_row_id,
    q,
    comment_thread
  ) => {
    this.commentsLoading = true;
    this.commentsService
      .getCommentsList(
        subs_id,
        page,
        size,
        category,
        batch_or_row_id,
        q,
        comment_thread
      )
      .subscribe({
        next: (resp) => {
          this.commentsLoading = false;
          this.threadPage = resp.page;
          this.threadSize = resp.page_size;
          this.threadTotalItems = resp.total_items;
          this.threadTotalPage = resp.total_pages;
          // if difference between total pages capacity and length of comment thread list is less than one page capacity
          // replace the last set of data with incoming data
          // using splice to replace
          if (
            this.threadPage * this.threadSize - this.commentThread.length <
            this.threadSize
          ) {
            this.commentThread.splice(
              (this.threadPage - 1) * this.threadSize,
              this.commentThread.length -
                (this.threadPage - 1) * this.threadSize,
              ...resp.result
            );
          } else {
            this.commentThread = [...this.commentThread, ...resp.result];
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.commentsLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * used to highlight existing tagged users in textarea
   * @param comment
   * @returns
   */
  getSelectedChoices = (comment): User[] => {
    return comment.tagged_users;
  };

  /**
   * edit comment
   * @param comment
   */
  editComment = (comment) => {
    this.isCommentChanged = false;
    this.attachmentID = null;
    this.selectedUsersEdited = comment.tagged_users;
    comment['edited_text'] = comment.text;
    this.isEditFileRemoved = false;
    this.filesEdit = [];
    this.editUploadcheck = false;
    this.isEditFileRemoved = false;
    comment['attachment'].hasOwnProperty('name')
      ? (this.isAllowAttachment = false)
      : (this.isAllowAttachment = true);
    // deactivate all other comment edits
    this.commentThread
      .filter((item) => item.comment_id != comment.comment_id)
      .forEach((row) => {
        row.editable = false;
      });
  };

  /**
   * delete comment from comment thread
   * @param comment_id
   * @param index
   */
  deleteComment = (comment_id, index) => {
    this.dialogRef = this.dialog.open(ConfirmDialog, {
      data: {
        content: $localize`Are you sure you want to delete this comment?`,
        status: 'DELETE COMMENTS',
        primarybtnName: $localize`Delete`,
        secondaryBtnName: $localize`Close`,
        trigger: (action) => {
          this.confirmDelete(comment_id, index);
        },
      },
    });
  };

  confirmDelete(comment_id, index) {
    this.dialogRef.afterClosed().subscribe((discard) => {
      if (discard) {
        this.commentsService
          .deleteComment(this.subscriptionId, comment_id)
          .subscribe({
            next: (resp) => {
              this.commentThread.splice(index, 1);
              this.snackbarService.openSnackBar(resp.detail, 'OK');
            },
            error: (HttpResponse: HttpErrorResponse) => {
              this.dataLoading = false;
              this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
            },
          });
      }
      this.dialogRef = null;
    });
  }

  /**
   * tag removal event in comment thread
   * @param e
   */
  taggedUserRemovedInCommentEdit = (e) => {};

  /**
   * event fired when comment thread section is scrolled
   * fetching new pages
   */
  onCommentThreadScroll = () => {
    // check if comment thread length is equal to total item capacity of pages
    // or check if total item size > total item capacity of pages
    // increment pageSize by 1
    if (this.threadTotalItems > this.commentThread.length) {
      if (this.commentThread.length == this.threadSize * this.threadPage) {
        this.threadPage++;
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.rowId,
          this.searchInComment,
          true
        );
      } else {
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.rowId,
          this.searchInComment,
          true
        );
      }
    }
  };

  /**
   * Request review btn
   * @param batchId
   * @param bucketValue
   */
  requestRework = (rowId, bucketValue) => {
    let obj = { bucket: bucketValue.toUpperCase() };
    this.rowId = this.nextProductId;
    this.beforeUndoBucket = this.bucket;
    this.reviewService.bucketUpdate(rowId, obj, this.subscriptionId).subscribe({
      next: (resp) => {
        let urlTree = this.router.parseUrl(this.router.url);
        urlTree.queryParams['bucket'] = bucketValue.toUpperCase();
        this.router.navigateByUrl(urlTree);
        if (resp['body'] != undefined) {
          this.getReviewProductDetail(
            this.searchedItems,
            this.searchedMultipleVals,
            this.start_date,
            this.end_date,
            this.row_id,
            this.bucket
          );
          this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
            duration: 30000,
            // passing args as data
            data: {
              response: resp.body.detail,
              module: this.subscriptionId,
              Id: rowId,
              onUndo: () => {
                let urlTree = this.router.parseUrl(this.router.url);
                urlTree.queryParams['bucket'] = this.beforeUndoBucket;
                this.router.navigateByUrl(urlTree);

                this.getReviewProductDetail(
                  this.searchedItems,
                  this.searchedMultipleVals,
                  this.start_date,
                  this.end_date,
                  this.row_id,
                  this.beforeUndoBucket
                );
              },
            },
          });
        }
      },
      error: (HttpError: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(HttpError.error, 'OK');
      },
    });
  };

  /**
   * Accept or Decline suggestions (all suggestion)
   * @param row_id,
   *  prediction_id, obj, module_slug,prediction_id,suggestion,is_accepted
   */
  acceptAllSuggestions = (item, row_id, is_accepted) => {
    this.rowId = this.nextProductId;
    this.reviewService
      .suggestionAccceptDecilneAll(this.subscriptionId, row_id, is_accepted)
      .subscribe({
        next: (resp) => {
          this.dataLoading = true;
          // splice entire row object
          let index = this.reviewData.indexOf(item);
          this.reviewData.splice(index, 1);
          this.getRowData(
            this.searchedItems,
            this.selectedFilters,
            this.start_date,
            this.end_date,
            this.subscriptionId,
            this.rowId
          );
          // this.matSnackbar.open(resp.detail, 'OK', {
          //   duration: 3000,
          // });
          this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
            duration: 30000,
            // passing args as data
            data: {
              response: resp.detail,
              module: this.subscriptionId,
              Id: this.rowId,
              onUndo: () => {
                this.getRowData(
                  this.searchedItems,
                  this.selectedFilters,
                  this.start_date,
                  this.end_date,
                  this.subscriptionId,
                  row_id
                );
              },
            },
          });
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * get a particular row updated data
   * probably during accept/reject
   * @param search
   * @param filter
   * @param start_date
   * @param end_date
   * @param subs_id
   * @param row_id
   * @returns
   */
  getRowData = (search, filter, start_date, end_date, subs_id, row_id) => {
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date,
      row_id,
      this.bucket
    );

    // this.reviewService
    //   .getReviewRow(search, filter, start_date, end_date, subs_id, row_id)
    //   .subscribe({
    //     next: (resp) => {
    //       this.reviewData = [];
    //       this.dataLoading = false;
    //       // this.isDataPresent = resp.result.length === 0 ? true : false;
    //       this.reviewList = resp.result.row_data;
    //       this.reviewData.push(this.reviewList);

    //       this.previousProductId = resp.result.previous;
    //       this.nextProductId = resp.result.next;
    //       // Current and Total items
    //       this.currentItem = resp.result.current_index;
    //       this.totalItems = resp.result.total_rows;
    //       this.reviewData.forEach((data) => {
    //         data.activeAttribute = data.predictions[0]?.display_name;
    //         let count = 0;
    //         data.main_attribute_count = count;
    //         // sort so that all items wit main_attribute true comes first
    //         data.predictions.sort(function (a, b) {
    //           if (b.main_attribute == true) {
    //             count++;
    //           }
    //           data.main_attribute_count = count;
    //           return b.main_attribute - a.main_attribute;
    //         });
    //       });
    //       this.predictionId = resp.result[0]?.predictions[0].prediction_id;
    //       this.predictionName = resp.result[0]?.predictions[0].display_name;
    //       this.rowId = resp.result.row_data.row_id;

    //       this.getCommentThread(
    //         this.subscriptionId,
    //         this.threadPage,
    //         this.threadSize,
    //         this.commentsListForType,
    //         this.rowId,
    //         this.searchInComment,
    //         true
    //       );
    //       return this.reviewData;
    //     },
    //   });
  };

  /**
   * Prev & next product
   * @param val
   * @returns
   */
  changetoPrevProduct = () => {
    this.dataLoading = true;
    this.row_id = this.previousProductId;
    if (this.previousProductId == 'None') {
      this.dataLoading = false;
      this.matSnackbar.open(
        $localize`Please use Next Button to view more products`,
        'OK',
        {
          duration: 5000,
        }
      );
      return;
    } else {
      this.content = '';
      this.getReviewProductDetail(
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date,
        this.row_id,
        this.bucket
      );
    }

    this.router.navigate([], {
      queryParams: {
        row_id: this.row_id,
        sub: this.subscriptionId,
        from: this.fromPage,
        bucket: this.bucket,
      },
      preserveFragment: false,
      queryParamsHandling: '',
    });
  };

  /**
   * Prev & next product
   * @param val
   * @returns
   */
  changetoNextProduct = () => {
    this.dataLoading = true;
    this.row_id = this.nextProductId;
    if (this.nextProductId == 'None') {
      this.dataLoading = false;
      this.matSnackbar.open(
        $localize`You have viewed all the products in this batch`,
        $localize`OK`,
        {
          duration: 5000,
        }
      );
      return;
    } else {
      this.content = '';
      this.getReviewProductDetail(
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date,
        this.row_id,
        this.bucket
      );
    }

    this.router.navigate([], {
      queryParams: {
        row_id: this.row_id,
        sub: this.subscriptionId,
        from: this.fromPage,
        bucket: this.bucket,
      },
      preserveFragment: false,
      queryParamsHandling: '',
    });
  };
  /**
   * Accept or Decline suggestions (all suggestion)
   * @param row_id,
   *  prediction_id, obj, module_slug,prediction_id,suggestion,is_accepted
   */
  // acceptAllSuggestions = (item, row_id, is_accepted) => {
  //   this.rowId = this.nextProductId;
  //   this.reviewService
  //     .suggestionAccceptDecilneAll(this.subscriptionId, row_id, is_accepted)
  //     .subscribe({
  //       next: (resp) => {
  //         this.dataLoading = true;
  //         // splice entire row object
  //         let index = this.reviewData.indexOf(item);
  //         this.reviewData.splice(index, 1);
  //         this.getRowData(
  //           this.searchedItems,
  //           this.selectedFilters,
  //           this.start_date,
  //           this.end_date,
  //           this.subscriptionId,
  //           this.rowId
  //         );
  //         // this.matSnackbar.open(resp.detail, 'OK', {
  //         //   duration: 3000,
  //         // });
  //         this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
  //           duration: 30000,
  //           // passing args as data
  //           data: {
  //             response: resp.detail,
  //             module: this.subscriptionId,
  //             Id: this.rowId,
  //             onUndo: () => {
  //               this.getRowData(
  //                 this.searchedItems,
  //                 this.selectedFilters,
  //                 this.start_date,
  //                 this.end_date,
  //                 this.subscriptionId,
  //                 row_id
  //               );
  //             },
  //           },
  //         });
  //       },
  //       error: (HttpResponse: HttpErrorResponse) => {
  //         this.snackbarService.openSnackBar(
  //           `${HttpResponse.error.detail}`,
  //           'OK'
  //         );
  //       },
  //     });
  // };

  previousBucket;
  updateBucket = (rowId, bucket) => {
    this.previousBucket = this.bucket;
    let obj = { bucket: bucket.toUpperCase() };
    this.productService
      .bucketUpdate(rowId, obj, this.subscriptionId)
      .subscribe({
        next: (resp) => {
          let urlTree = this.router.parseUrl(this.router.url);
          urlTree.queryParams['bucket'] = bucket.toUpperCase();
          this.router.navigateByUrl(urlTree);
          this.getReviewProductDetail(
            this.searchedItems,
            this.searchedMultipleVals,
            this.start_date,
            this.end_date,
            this.rowId,
            bucket.toUpperCase()
          );
          this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
            duration: 30000,
            // passing args as data
            data: {
              response: resp.detail,
              module: this.subscriptionId,
              Id: rowId,
              onUndo: () => {
                // modify url with undo bucket
                let urlTree = this.router.parseUrl(this.router.url);
                urlTree.queryParams['bucket'] =
                  this.undoBucketValue.toUpperCase();
                this.router.navigateByUrl(urlTree);

                this.getReviewProductDetail(
                  this.searchedItems,
                  this.searchedMultipleVals,
                  this.start_date,
                  this.end_date,
                  this.row_id,
                  this.undoBucketValue
                );
              },
            },
          });
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  /**
   * Attachment selection
   * @param event
   * @param mode
   */
  onFileSelected = (event, mode) => {
    if (
      event.target.files.length > 0 &&
      event.target.files[0].size <= 10000000
    ) {
      if (mode === 'create') {
        this.uploadCheck = true;
        this.uploadedFile = event.target.files[0];
        this.files.push(this.uploadedFile);
      }

      if (mode === 'edit') {
        this.editUploadcheck = true;
        this.filesEdit.push(event.target.files[0]);
        this.isAllowAttachment = false;
        this.isCommentChanged = true;
      }
    } else {
      this.matSnackbar.open('please upload file size less than 10MB', 'OK', {
        duration: 3000,
      });
    }
  };

  toggleFullscreen(isActive, mode) {
    isActive ? this.openFullscreen() : this.closeFullscreen();
  }

  openFullscreen() {
    const elem = this.divRef.nativeElement;
    if (elem.requestFullscreen) {
      elem.requestFullscreen();
    } else if (elem.msRequestFullscreen) {
      /* IE/Edge */
      elem.msRequestFullscreen();
    } else if (elem.mozRequestFullScreen) {
      /* Firefox */
      elem.mozRequestFullScreen();
    } else if (elem.webkitRequestFullscreen) {
      /* Chrome, Safari and Opera */
      elem.webkitRequestFullscreen();
    }
  }

  closeFullscreen() {
    if (this.document.exitFullscreen) {
      this.document.exitFullscreen();
    } else if (this.document.mozCancelFullScreen) {
      /* Firefox */
      this.document.mozCancelFullScreen();
    } else if (this.document.webkitExitFullscreen) {
      /* Chrome, Safari and Opera */
      this.document.webkitExitFullscreen();
    } else if (this.document.msExitFullscreen) {
      /* IE/Edge */
      this.document.msExitFullscreen();
    }
  }

  /**
   * called on content change
   * to trim values if empty
   * @param e
   * @param mode
   * @param comment
   */
  onContentChange(e, mode, comment?) {
    // to check if the commemt content changed
    if (mode === 'edit') {
      this.isCommentChanged = true;
    }
    // to check if valid html
    if (e.html) {
      let result = this.extractContent(e.html);
      if (result === '') {
        if (mode === 'create' && !this.content.includes('<img')) {
          this.content = '';
        }
        if (mode === 'edit' && !comment.edited_text.includes('<img')) {
          comment.edited_text = '';
        }
      }
    }
  }

  /**
   * extract content from html tag
   * @param value
   * @returns
   */
  extractContent(value) {
    let span = document.createElement('div');
    span.innerHTML = value;
    return span.textContent.trim() || span.innerText.trim();
  }

  /**
   * editor instance
   * @param editorInstance
   * @param mode
   */
  getEditorInstance(editorInstance: any, mode?) {
    if (mode && mode === 'edit') {
      this.editorRefEdit = editorInstance;
      this.attachPasteListener('edit');
    } else {
      this.editorRef = editorInstance;
      this.attachPasteListener('create');
    }
  }

  /**
   * paste event handler
   * @param mode
   */
  attachPasteListener(mode) {
    if (mode === 'create') {
      this.editorRef.root.addEventListener('paste', (e) => {
        // using clipboard data as quill didnt return properly
        const clipboardData = e.clipboardData || (<any>window).clipboardData;
        if (clipboardData.files.length === 0) {
          return;
        } else if (clipboardData.files[0].type.startsWith('image/')) {
          // prevent clipboard from pasting image as quill is already doing
          e.preventDefault();
          const file = clipboardData.files[0];
          this.insertImage('', 'copy', file);
        }
      });
    }
    if (mode === 'edit') {
      this.editorRefEdit.root.addEventListener('paste', (e) => {
        const clipboardData = e.clipboardData || (<any>window).clipboardData;
        if (clipboardData.files.length === 0) {
          return;
        } else if (clipboardData.files[0].type.startsWith('image/')) {
          e.preventDefault();
          const file = clipboardData.files[0];
          this.insertImage('', 'edit', file);
        }
      });
    }
  }

  /**
   * inserting image and starting loader on upload
   * @param event
   * @param mode
   * @param file
   */
  insertImage(event, mode?, file?) {
    if (event) {
      file = (event.target as HTMLInputElement).files[0];
    }
    if (file && file.size <= 10000000) {
      this.startEditorLoader(mode);
      this.uploadImage(file, mode);
    }
  }

  startEditorLoader(mode?) {
    mode && mode === 'edit'
      ? (this.isImgUploadEdit = true)
      : (this.isImgUpload = true);
  }

  stopEditorLoader(mode?) {
    mode && mode === 'edit'
      ? (this.isImgUploadEdit = false)
      : (this.isImgUpload = false);
  }

  /**
   * uploading embedded image to get the signed url
   * @param file
   * @param mode
   */
  uploadImage(file?: File, mode?) {
    this.commentsService
      .uploadImage(file, mode, this.subscriptionId)
      .subscribe({
        next: (res) => {
          if (mode && mode === 'edit') {
            this.imgFileInputEdit.nativeElement.value = '';
          } else {
            this.imgFileInput.nativeElement.value = ''; // allow to select the same file
          }
          this.embedImage(res['redirect_url'], mode);
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.imgFileInput.nativeElement.value = ''; // allow to select the same file
          this.imgFileInputEdit.nativeElement.value = '';
          this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
            duration: 3000,
          });
        },
      });
  }

  /**
   * embed signed url in img src
   * @param url
   * @param mode
   * @returns
   */
  embedImage(url: string, mode?) {
    if (!this.editorRef && !this.editorRefEdit) {
      return;
    }
    if (mode && mode === 'edit') {
      const range = this.editorRefEdit.getSelection();
      this.editorRefEdit.insertEmbed(range, 'image', url, 'user');
    } else {
      const range = this.editorRef.getSelection();
      this.editorRef.insertEmbed(range, 'image', url, 'user');
    }
    this.stopEditorLoader(mode);
  }

  /**
   * handles post/update comment
   * @param mode
   * @param comment
   */
  handler(mode, comment?) {
    if (mode === 'create') {
      this.postComment(this.content, this.selectedUsers, this.files);
    }
    if (mode === 'edit') {
      this.updateComment(comment, this.selectedUsersEdited, this.filesEdit);
    }
  }

  /**
   * update comment
   * @param comment
   * @param user
   * @param file
   */
  updateComment = (comment, user, file) => {
    const addedUsers = []; //only added users
    const IDs = [];
    let obj = {
      comment: comment['edited_text'],
      tagged_users: addedUsers, //default
      attachment_name: file.length > 0 ? file[0].name : file,
      remove_attachment_id: this.attachmentID,
      file_hash_ids: IDs,
      remove_tagged_users: [], // default
    };
    if (obj.comment == null) {
      obj.comment = '';
    }
    // get editor content
    const { ops } = this.editorRefEdit.getContents();
    const holdEditorUsers = [];
    // check for tagged users
    const result = ops.reduce((acc, { insert }) => {
      if (typeof insert === 'string') {
      } else {
        if (insert.hasOwnProperty('mention')) {
          const existingUser = this.selectedUsersEdited.find(
            (that) => insert.mention.id === that.username
          );
          if (!existingUser) {
            // newly added users
            addedUsers.push(insert.mention.id);
          }
          holdEditorUsers.push(insert.mention.id);
        }
        if (insert.hasOwnProperty('image')) {
          const test = insert.image.match(/attachments\/(.*)\//);
          IDs.push(test[1]);
        }
      }
      return acc;
    }, '');
    // check for removed users
    if (this.selectedUsersEdited.length > 0) {
      obj.remove_tagged_users = this.selectedUsersEdited
        .filter((each) => !holdEditorUsers.includes(each.username))
        .map((each) => each.username);
    }
    // remove duplicate tagged users [allowed in editor]
    obj.tagged_users = [...new Set(addedUsers)];
    this.commentsService
      .updateComment(this.subscriptionId, comment.comment_id, obj)
      .subscribe({
        next: (resp) => {
          this.filesEdit = [];
          this.selectedUsersEdited = [];
          this.attachmentID = null;
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          comment.editable = false;
          // if no ext attchment
          if (!resp['attachments'].hasOwnProperty('signed_url')) {
            this.getCommentThread(
              this.subscriptionId,
              (this.threadPage = 1),
              this.threadSize,
              this.commentsListForType,
              this.rowId,
              this.searchInComment,
              true
            );
            return;
          }
          // if external attachment
          if (
            resp['attachments'].hasOwnProperty('signed_url') &&
            resp['attachments']['signed_url'] != ''
          ) {
            this.fileUploadService
              .getSessionURI(resp['attachments']['signed_url'], file[0])
              .subscribe({
                next: (progress) => {
                  // call comments list api to update the latest comments
                  this.getCommentThread(
                    this.subscriptionId,
                    (this.threadPage = 1),
                    this.threadSize,
                    this.commentsListForType,
                    this.rowId,
                    this.searchInComment,
                    true
                  );
                  // upload api called to notify BE
                  this.fileUploadService
                    .uploadCompelte(
                      true,
                      comment.comment_id,
                      this.subscriptionId
                    )
                    .subscribe({
                      next: (resp) => {},
                    });
                },
                error: (HttpResponse: HttpErrorResponse) => {
                  this.dataLoading = false;
                  this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
                    duration: 3000,
                  });
                },
              });
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(HttpResponse.error, 'OK');
        },
      });
  };

  clear = (index) => {
    this.uploadCheck = false;
    this.files.splice(index, 1);
    // this.files = this.files;
  };

  clearEditAttachment(comment, index) {
    this.filesEdit = [];
    this.isEditFileRemoved = true;
    this.isAllowAttachment = true;
    this.isCommentChanged = false;
    this.attachmentID = comment['attachment']['attachment_id'];
  }

  onCancelEdit(comment) {
    comment.editable = false;
    this.attachmentID = null;
  }
}
