import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../shared/shared.module';
import { CommentsComponent } from './comments/comments.component';
import { ProductDetailsComponent } from './product-details/product-details.component';
import { ReviewModeComponent } from './review-mode/review-mode.component';
import { CustomersRoutingModule } from './details-routing.module';

@NgModule({
  declarations: [
    ProductDetailsComponent,
    ReviewModeComponent,
    CommentsComponent,
  ],
  imports: [CommonModule, CustomersRoutingModule, SharedModule],
})
export class DetailsModule {}
