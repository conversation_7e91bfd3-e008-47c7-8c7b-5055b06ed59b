<section class="approve-dialog" fxLayout="column" fxLayoutAlign="start center">
  <h3 i18n>Approve Batch</h3>
  <div class="content" *ngIf="batchData" fxLayout="column" fxLayoutGap="10px">
    <span i18n>Out of {{batchData['total_rows']}} SKUs in Batch <b>{{batchId}}</b>, there are: </span>
    <div fxLayout="column" fxLayoutGap="10px">
      <span *ngIf="batchData['accepted'].toString()" i18n>{{batchData['accepted']}} in 'Accepted'</span>
      <span *ngIf="batchData['review'].toString()" i18n>{{batchData['review']}} in 'Review'</span>
      <span *ngIf="batchData['in_progress'].toString()" i18n>{{batchData['in_progress']}} in 'Progress'</span>
      <span *ngIf="batchData['rework'].toString()" i18n>{{batchData['rework']}} in 'Rework'</span>
      <span *ngIf="batchData['insufficient_data'].toString()" i18n>{{batchData['insufficient_data']}} in 'Insufficient
        Data'</span>
    </div>
    <span i18n>
      Are you sure you want to approve the entire batch? No further processing of SKUs is possible after the batch is
      marked 'Approved'.
    </span>
    <div class="action-buttons" fxLayout="row" fxLayoutAlign="center" fxLayoutGap="20px">
      <button mat-button (click)="recordUserAction('close')" i18n>
        Close
      </button>
      <button mat-button class="stroked-btn-primary" (click)="recordUserAction('reroute')" i18n>
        View Details
      </button>
      <button mat-button class="filled-btn-primary" (click)="recordUserAction('approve')" i18n>
        Approve Anyway
      </button>
    </div>
  </div>
  <div class="loading-spinner center-placement" *ngIf="!batchData" fxLayoutAlign="center center">
    <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
  </div>


</section>
