import { Component, OnInit, ViewChild } from '@angular/core';
import { SidePanelService } from '../../services/side-panel.service';
import { ChartConfiguration, ChartEvent, ChartType } from 'chart.js';
import { HomeService } from '../../services/home.service';
import { UserService } from 'src/app/services/user.service';
import { Params, Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
})
export class SettingsComponent implements OnInit {
  subscriptionId: any;
  stats: any;
  dataLoading: boolean = false;
  apiUsageData;
  filter;
  timePeriod: any[] = [
    { name: $localize`Monthly`, value: 'monthly' },
    { name: $localize`Weekly`, value: 'weekly' },
  ];
  // line chart configs
  public lineChartOptions: ChartConfiguration['options'] = {
    elements: {
      line: {
        tension: 0,
      },
    },
    scales: {
      // We use this empty structure as a placeholder for dynamic theming.
      x: {
        grid: {
          borderDash: [4],
        },
      },
      'y-axis-0': {
        position: 'left',
        grid: {
          borderDash: [4],
        },
      },
    },
    plugins: {
      legend: {
        display: false,
      },
    },
  };
  public lineChartType: ChartType = 'line';
  lineChartData;

  constructor(
    private sidepanel: SidePanelService,
    private homeService: HomeService,
    private router: Router,
    private userService: UserService,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit() {
    this.dataLoading = true;
    this.filter = 'monthly';
    // retrieve subscription id
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.subscriptionId = params.sub;
    });
    //stats card
    this.homeService.getStats(this.subscriptionId).subscribe({
      next: (resp) => {
        this.dataLoading = false;
        this.stats = resp.result;
      },
    });
    this.getApiUsageStats(this.filter);
  }

  /**
   * Api usage data
   * @param filter
   */
  getApiUsageStats = (filter) => {
    this.userService.apiUsage(this.subscriptionId, filter).subscribe({
      next: (resp) => {
        this.apiUsageData = resp;
        this.lineChartData = {
          datasets: [
            {
              data: this.apiUsageData.data,
              borderWidth: 1,
              pointRadius: 3,
              pointHoverRadius: 6,
              pointBorderColor: 'black',
              pointBackgroundColor: '#EDEFF5',
              pointStyle: 'circle',
            },
          ],
          labels: this.apiUsageData.label,
        };
      },
    });
  };
}
