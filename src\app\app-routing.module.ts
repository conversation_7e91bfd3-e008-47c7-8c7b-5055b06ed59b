import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './components/home/<USER>';
import { ProductsComponent } from './components/products/products.component';
import { ReviewComponent } from './components/review/review.component';
import { SettingsComponent } from './components/settings/settings.component';
import { LoadingComponent } from './components/loading/loading.component';
import { HelpComponent } from './components/help/help.component';
import { AuthGuard } from '@auth0/auth0-angular';
import { AppAuthGuard } from './_guards/auth.guard';
import { OfflineComponent } from './components/offline/offline.component';
import { PreloadAllModules } from '@angular/router';
const routes: Routes = [
  {
    path: 'home',
    component: HomeComponent,
    data: { title: $localize`Home` },
    canActivate: [AppAuthGuard],
  },
  {
    path: 'loading',
    component: LoadingComponent,
    data: { title: $localize`Loading` },
    canActivate: [AuthGuard],
  },
  {
    path: 'review',
    component: ReviewComponent,
    data: { title: $localize`Review` },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'products',
    component: ProductsComponent,
    data: { title: $localize`Products` },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'settings',
    component: SettingsComponent,
    data: { title: $localize`Settings` },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'offline',
    component: OfflineComponent,
    data: { title: $localize`You are offline` },
    // canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'help',
    component: HelpComponent,
    data: { title: $localize`Help` },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'details',
    loadChildren: () =>
      import('./details/details.module').then((m) => m.DetailsModule),
  },
  {
    path: '**',
    component: HomeComponent,
    canActivate: [AuthGuard, AppAuthGuard],
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      preloadingStrategy: PreloadAllModules,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
