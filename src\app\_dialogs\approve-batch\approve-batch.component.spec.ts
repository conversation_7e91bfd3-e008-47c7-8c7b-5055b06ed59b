import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ApproveBatchDialog } from './approve-batch.component';

describe('ApproveBatchDialog', () => {
  let component: ApproveBatchDialog;
  let fixture: ComponentFixture<ApproveBatchDialog>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ApproveBatchDialog],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ApproveBatchDialog);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
