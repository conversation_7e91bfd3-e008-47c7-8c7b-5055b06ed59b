<!--Header Starts-->
<div class="top-nav" fxLayoutAlign="center center">
  <div class="nav-bar" layout="row" fxFlex="100" fxLayoutAlign.xs="space-between center"
    fxLayoutAlign="space-between center">
    <!--Logo -->
    <div class="top-nav-logo" fxLayout="row" fxLayoutAlign="space-between center">
      <a class="top-nav-logo-link" href="{{ user?.dashboard_url }}" target="_self">
        <img src="assets/images/datax-updated-logo/dataX.aiWhite.svg" class="svg-icon" />
      </a>
      <span class="app-title"> {{user?.module_name}} </span>
    </div>
    <!-- topnav actions -->
    <div class="top-nav__menu">
      <div fxLayout="row" class="menu-notification" style="margin-top: 20px">
        <!-- notification -->
        <!-- <a
          class="top-nav__menu-notification"
          [matMenuTriggerFor]="notification"
        >
          <div fxLayout="column" fxLayoutAlign="center center">
            <p class="dot"></p>
            <img src="assets/images/top-nav-svg/notification.svg" />
          </div>
        </a>
        <mat-menu #notification="matMenu" class="notification-drop-down">
          <div
            fxLayout="row"
            fxLayoutAlign="space-between center"
            class="notification-header"
          >
            <p class="notification-display-name">Notification</p>
            <p class="mark-read text-theme-primary">Mark all as read</p>
          </div>

          <div
            *ngFor="let item of [1, 2]"
            fxLayout="row"
            fxLayoutAlign="flex-start"
            mat-menu-item
            class="notification-item"
          >
            <img [src]="userDisplayPicture" />

            <div fxLayout="column" fxLayoutAlign="space-between start">
              <p class="notification-disply-name">Bill Turner</p>
              <p class="notification-message">mentioned you in a comment.</p>
            </div>

            <p class="notification-time">2mins ago</p>
          </div>
        </mat-menu> -->
        <!-- User display picture -->
        <!-- <a class="top-nav__menu-link" *ngIf="user"> -->
        <div *ngIf="user?.profile_picture" fxLayout="column" fxLayoutAlign="start center" style="margin-top: 5px">
          <img [src]="user?.profile_picture" class="avatar" />
        </div>
        <!-- </a> -->
        <!-- User role -->
        <!-- <a  *ngIf="user"> -->
        <div class="top-nav__menu-user" fxLayout="column" fxLayoutAlign="start start">
          <p class="display-name" [matTooltip]="user?.name.length > 18 ? user?.name : null" matTooltipPosition="above">{{
                user?.name.length > 18
                  ? (user?.name | slice: 0:18) + ".."
                  : user?.name
              }}</p>
          <p class="role" [matTooltip]="user?.email.length > 18 ? user?.email : null" matTooltipPosition="above">{{
                user?.email.length > 18
                  ? (user?.email | slice: 0:18) + ".."
                  : user?.email
              }}</p>
        </div>
        <!-- </a> -->
        <!-- Dropdown action buttons -->
        <div class="menu-drop" [matMenuTriggerFor]="menu" #t="matMenuTrigger" *ngIf="user">
          <!-- <mat-icon mat-icon-button  class="material-icons-outlined">
                arrow_drop_down_circle</mat-icon> -->
          <img src="assets/images/top-nav-svg/drop-arrow.svg" [src]="
          t.menuOpen
            ? 'assets/images/top-nav-svg/up-arrow-white.svg'
            : 'assets/images/top-nav-svg/drop-arrow.svg'
        " />
        </div>
        <mat-menu #menu="matMenu">
          <button mat-menu-item *ngIf="auth.isAuthenticated$ | async" (click)="logUserOut()">
            <mat-icon>power_settings_new</mat-icon>
            <span class="menu-item" i18n>Log Out</span>
          </button>
        </mat-menu>
        <!-- client logo -->
        <div class="client_logo" *ngIf="user?.client_logo">
          <img src="{{ user?.client_logo }}" />
        </div>
      </div>
    </div>
  </div>
</div>
