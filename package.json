{"name": "module-rs2-a", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 3000  --disable-host-check --host m2.app-local.datax.ai --ssl true --configuration=en", "build": "ng build", "postbuild": "bash ./build-utils/post-build.sh", "build-dev": "ng build --configuration='dev' && npm run postbuild", "build-staging": "ng build --configuration='stag' && npm run postbuild", "build-prod": "ng build --configuration='production' && npm run postbuild", "watch": "ng build --watch --configuration development", "build:stats": "ng build --stats-json", "analyze": "webpack-bundle-analyzer dists/moduleR2E/stats.json", "test": "ng test"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^6.0.3", "@angular/animations": "~12.1.0", "@angular/cdk": "^12.2.13", "@angular/common": "~12.1.0", "@angular/compiler": "~12.1.0", "@angular/core": "~12.1.0", "@angular/flex-layout": "^13.0.0-beta.36", "@angular/forms": "~12.1.0", "@angular/material": "^12.2.13", "@angular/platform-browser": "~12.1.0", "@angular/platform-browser-dynamic": "~12.1.0", "@angular/router": "~12.1.0", "@auth0/auth0-angular": "^1.8.2", "chart.js": "^3.7.0", "moment": "^2.29.1", "ng2-charts": "^3.0.8", "ngx-dropzone": "^3.0.0", "ngx-infinite-scroll": "^10.0.1", "ngx-pinch-zoom": "^2.6.2", "ngx-quill": "^15.0.0", "quill-mention": "^3.1.0", "rxjs": "~6.6.0", "simple-color-picker": "^1.0.5", "tslib": "^2.2.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-builders/custom-webpack": "^12.0.0", "@angular-devkit/build-angular": "~12.1.4", "@angular/cli": "~12.1.4", "@angular/compiler-cli": "~12.1.0", "@angular/localize": "^12.1.5", "@types/jasmine": "~3.8.0", "@types/node": "^12.11.1", "compression-webpack-plugin": "^10.0.0", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.3.2", "webpack-bundle-analyzer": "^4.5.0"}}